var xa=Object.defineProperty;var Ma=(r,e,t)=>e in r?xa(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var je=(r,e,t)=>Ma(r,typeof e!="symbol"?e+"":e,t);import{aK as Aa,b5 as Ta,b6 as Na,f as $t,b as l,w as vt,aD as Zr,aA as _r,A as st,C as b,D as at,J as g,H as V,_ as n,L as f,t as h,M as v,a3 as xe,Q as ge,ab as Mt,W as Ze,aF as hs,T as de,V as R,P as wt,I as rt,X as me,Y as m,m as F,K as w,$ as ht,R as es,S as Ra,F as ot,G as Se,a4 as nt,a2 as Et,z as Ia,l as rs,Z as Ea,a as _s,a1 as La,N as xt,al as Rr,a7 as za,b7 as Or,ay as Ys,v as $s,u as ps,b8 as Pr,aE as hr,a5 as Ts,B as Ir,ak as Za,b0 as Fr,a6 as ra,b9 as Oa,az as Pa}from"./SpinnerAugment-Blmu0lvP.js";import"./design-system-init-BQyB2H9Z.js";import{W as Je,d as St,e as ut,I as as,b as Fa,c as ct,i as Gt,a as ja,h as Hs,f as jr,g as Da,H as pr}from"./IconButtonAugment-PsuQzIqb.js";import{R as Dr,M as Ua}from"./message-broker-1nzlxrR9.js";import{G as Va,S as qa,b as Ha,N as Ba,L as Ga,c as mt,M as ls,D as Ja,F as Wa,f as aa,R as Ka,d as Ur,T as na,e as Ya,C as Xa,P as vr,g as Qa,h as en,i as tn,A as sn,j as rn,k as an}from"./trash-can-BYT4gceD.js";import{Q as mr,a2 as Ft,O as Ge,i as gr,t as wr,T as ts,D as tt,a3 as nn,C as ia,E as oa,a4 as Js,f as fr,A as Vr,g as on,h as ln,R as dn}from"./index-DrKwiXGp.js";import{G as cn,D as Ws,C as un,P as ms,B as Cr,T as br,a as la,S as $r,c as hn}from"./download-DQBL2L3a.js";import{o as Xs}from"./keypress-DD1aQVr0.js";import{V as da}from"./VSCodeCodicon-CMFxg1-l.js";import{A as pn}from"./async-messaging-CT_HnajV.js";import{c as Sr}from"./svelte-component-D1UF0iyq.js";import{k as vn,C as ca,a as mn,T as Qs}from"./CollapseButtonAugment-DtvbSMsW.js";import{D as gn}from"./Drawer-BfzrYLZA.js";import{b as ua,T as Dt,a as us,p as fn}from"./CardAugment-BINJ-GT9.js";import{B as Xe}from"./ButtonAugment-JLgrdIjM.js";import{C as Ns}from"./CalloutAugment-faiqhQ9C.js";import{E as yn}from"./ellipsis-DC-VCN8w.js";import{P as _n}from"./pen-to-square-BJ-HIlSJ.js";import{T as ha,S as wn}from"./TextAreaAugment-BU46gVeQ.js";import{C as Cn}from"./copy-Ch7fLNyX.js";import{C as Er}from"./chevron-down-CbWnEp8T.js";import{M as bn}from"./index-CCRUnEQy.js";import{M as $n}from"./MarkdownEditor-DIDpINwZ.js";import{R as Sn}from"./RulesModeSelector-CIuvGDS8.js";import{M as pa}from"./ModalAugment-BemmrpQD.js";import"./types-CGlLNakm.js";import"./focusTrapStack-DKcV5UNJ.js";import"./isObjectLike-CQD9DZVf.js";import"./BaseTextInput-CEVTGWJo.js";import"./index-Bzs0KkUY.js";import"./index-CG1kCc6X.js";const kn=[];function qr(r,e=!1){return er(r,new Map,"",kn)}function er(r,e,t,s,a=null){if(typeof r=="object"&&r!==null){var o=e.get(r);if(o!==void 0)return o;if(r instanceof Map)return new Map(r);if(r instanceof Set)return new Set(r);if(Aa(r)){var i=Array(r.length);e.set(r,i),a!==null&&e.set(a,i);for(var d=0;d<r.length;d+=1){var c=r[d];d in r&&(i[d]=er(c,e,t,s))}return i}if(Ta(r)===Na){for(var u in i={},e.set(r,i),a!==null&&e.set(a,i),r)i[u]=er(r[u],e,t,s);return i}if(r instanceof Date)return structuredClone(r);if(typeof r.toJSON=="function")return er(r.toJSON(),e,t,s,r)}if(r instanceof EventTarget)return r;try{return structuredClone(r)}catch{return r}}const Ss={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class xn{constructor(e,t=Ss){je(this,"timerId",null);je(this,"currentMS");je(this,"step",0);je(this,"params");this.callback=e;const s={...t};s.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),s.maxMS=Ss.maxMS),s.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),s.initialMS=Ss.initialMS),s.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),s.mult=Ss.mult),s.maxSteps!==void 0&&s.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),s.maxSteps=Ss.maxSteps),this.params=s,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}var Mn=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function An(r){var e=Mn();l(r,e)}var Tn=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function Nn(r){var e=Tn();l(r,e)}var Rn=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function Ht(r){var e=Rn();l(r,e)}var qe,kr,In=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function En(r){var e=In();l(r,e)}class Rs{constructor(e){je(this,"configs",vt([]));je(this,"pollingManager");je(this,"_enableDebugFeatures",vt(!1));je(this,"_settingsComponentSupported",vt({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));je(this,"_enableAgentMode",vt(!1));je(this,"_enableAgentSwarmMode",vt(!1));je(this,"_enableNativeRemoteMcp",vt(!0));je(this,"_hasEverUsedRemoteAgent",vt(!1));je(this,"_enableInitialOrientation",vt(!1));je(this,"_userTier",vt("unknown"));je(this,"_guidelines",vt({}));this._host=e,this.pollingManager=new xn(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,s=e.oauthUrl;if(e.identifier.hostName===mr.remoteToolHost){let a=e.identifier.toolId;switch(typeof a=="string"&&/^\d+$/.test(a)&&(a=Number(a)),a){case Ft.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:cn,requiresAuthentication:t,authUrl:s};case Ft.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Ga,requiresAuthentication:t,authUrl:s};case Ft.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:Nn,requiresAuthentication:t,authUrl:s};case Ft.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Ba,requiresAuthentication:t,authUrl:s};case Ft.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:An,requiresAuthentication:t,authUrl:s};case Ft.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Ha,requiresAuthentication:t,authUrl:s};case Ft.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:qa,requiresAuthentication:t,authUrl:s};case Ft.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Va,requiresAuthentication:t,authUrl:s};case Ft.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled RemoteToolId: ${a}`)}}else if(e.identifier.hostName===mr.localToolHost){const a=e.identifier.toolId;switch(a){case Ge.readFile:case Ge.editFile:case Ge.saveFile:case Ge.launchProcess:case Ge.killProcess:case Ge.readProcess:case Ge.writeProcess:case Ge.listProcesses:case Ge.waitProcess:case Ge.openBrowser:case Ge.clarify:case Ge.onboardingSubAgent:case Ge.strReplaceEditor:case Ge.remember:case Ge.diagnostics:case Ge.webFetch:case Ge.setupScript:case Ge.readTerminal:case Ge.gitCommitRetrieval:case Ge.memoryRetrieval:case Ge.startWorkerAgent:case Ge.readWorkerState:case Ge.waitForWorkerAgent:case Ge.sendInstructionToWorkerAgent:case Ge.stopWorkerAgent:case Ge.deleteWorkerAgent:case Ge.readWorkerAgentEdits:case Ge.applyWorkerAgentEdits:case Ge.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:Ht,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled LocalToolType: ${a}`)}}else if(e.identifier.hostName===mr.sidecarToolHost){const a=e.identifier.toolId;switch(a){case mt.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:ls,requiresAuthentication:t,authUrl:s};case mt.shell:return{displayName:"Shell",description:"Shell",icon:ls,requiresAuthentication:t,authUrl:s};case mt.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:ls,requiresAuthentication:t,authUrl:s};case mt.view:return{displayName:"File View",description:"File Viewer",icon:ls,requiresAuthentication:t,authUrl:s};case mt.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:ls,requiresAuthentication:t,authUrl:s};case mt.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:En,requiresAuthentication:t,authUrl:s};case mt.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Wa,requiresAuthentication:t,authUrl:s};case mt.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:Ht,requiresAuthentication:t,authUrl:s};case mt.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Ja,requiresAuthentication:t,authUrl:s};case mt.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:ls,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled SidecarToolType: ${a}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:s}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case Je.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(t.data.enableAgentSwarmMode),t.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(t.data.hasEverUsedRemoteAgent),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),t.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(t.data.enableNativeRemoteMcp),!0;case Je.toolConfigDefinitionsResponse:return this.configs.update(s=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(a=>{const o=s.find(i=>i.name===a.name);return o?{...o,displayName:a.displayName,description:a.description,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,isConfigured:a.isConfigured,toolApprovalConfig:a.toolApprovalConfig}:a})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(s=>{const a=this.transformToolDisplay(s),o=t.find(d=>d.name===s.definition.name),i=(o==null?void 0:o.isConfigured)??!a.requiresAuthentication;return{config:(o==null?void 0:o.config)??{},configString:JSON.stringify((o==null?void 0:o.config)??{},null,2),isConfigured:i,name:s.definition.name.toString(),displayName:a.displayName,description:a.description,identifier:s.identifier,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:s.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return Zr(this.configs,e=>{const t=e.filter(a=>this.isDisplayableTool(a)),s=new Map;for(const a of t)s.set(a.displayName,a);return Array.from(s.values()).sort((a,o)=>{const i={GitHub:1,Linear:2,Notion:3},d=Number.MAX_SAFE_INTEGER,c=i[a.displayName]||d,u=i[o.displayName]||d;return c<d&&u<d||c===d&&u===d?c!==u?c-u:a.displayName.localeCompare(o.displayName):c-u})})}getPretendNativeToolDefs(){return Zr(this.configs,e=>this.getEnableNativeRemoteMcp()?aa(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:Je.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:Je.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}updateToolApprovalConfig(e,t){this.configs.update(s=>s.map(a=>a.identifier.toolId===e.toolId&&a.identifier.hostName===e.hostName?{...a,toolApprovalConfig:t}:a))}getSettingsComponentSupported(){return this._settingsComponentSupported}}je(Rs,"key","toolConfigModel");(function(r){r.assertEqual=e=>e,r.assertIs=function(e){},r.assertNever=function(e){throw new Error},r.arrayToEnum=e=>{const t={};for(const s of e)t[s]=s;return t},r.getValidEnumValues=e=>{const t=r.objectKeys(e).filter(a=>typeof e[e[a]]!="number"),s={};for(const a of t)s[a]=e[a];return r.objectValues(s)},r.objectValues=e=>r.objectKeys(e).map(function(t){return e[t]}),r.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},r.find=(e,t)=>{for(const s of e)if(t(s))return s},r.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,r.joinValues=function(e,t=" | "){return e.map(s=>typeof s=="string"?`'${s}'`:s).join(t)},r.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(qe||(qe={})),function(r){r.mergeShapes=(e,t)=>({...e,...t})}(kr||(kr={}));const B=qe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),jt=r=>{switch(typeof r){case"undefined":return B.undefined;case"string":return B.string;case"number":return isNaN(r)?B.nan:B.number;case"boolean":return B.boolean;case"function":return B.function;case"bigint":return B.bigint;case"symbol":return B.symbol;case"object":return Array.isArray(r)?B.array:r===null?B.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?B.promise:typeof Map<"u"&&r instanceof Map?B.map:typeof Set<"u"&&r instanceof Set?B.set:typeof Date<"u"&&r instanceof Date?B.date:B.object;default:return B.unknown}},S=qe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class bt extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(o){return o.message},s={_errors:[]},a=o=>{for(const i of o.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let d=s,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(d[u]=d[u]||{_errors:[]},d[u]._errors.push(t(i))):d[u]=d[u]||{_errors:[]},d=d[u],c++}}};return a(this),s}static assert(e){if(!(e instanceof bt))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,qe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}bt.create=r=>new bt(r);const gs=(r,e)=>{let t;switch(r.code){case S.invalid_type:t=r.received===B.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case S.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,qe.jsonStringifyReplacer)}`;break;case S.unrecognized_keys:t=`Unrecognized key(s) in object: ${qe.joinValues(r.keys,", ")}`;break;case S.invalid_union:t="Invalid input";break;case S.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${qe.joinValues(r.options)}`;break;case S.invalid_enum_value:t=`Invalid enum value. Expected ${qe.joinValues(r.options)}, received '${r.received}'`;break;case S.invalid_arguments:t="Invalid function arguments";break;case S.invalid_return_type:t="Invalid function return type";break;case S.invalid_date:t="Invalid date";break;case S.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:qe.assertNever(r.validation):t=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case S.too_small:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case S.too_big:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case S.custom:t="Invalid input";break;case S.invalid_intersection_types:t="Intersection results could not be merged";break;case S.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case S.not_finite:t="Number must be finite";break;default:t=e.defaultError,qe.assertNever(r)}return{message:t}};let va=gs;function tr(){return va}const sr=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,o=[...t,...a.path||[]],i={...a,path:o};if(a.message!==void 0)return{...a,path:o,message:a.message};let d="";const c=s.filter(u=>!!u).slice().reverse();for(const u of c)d=u(i,{data:e,defaultError:d}).message;return{...a,path:o,message:d}};function P(r,e){const t=tr(),s=sr({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===gs?void 0:gs].filter(a=>!!a)});r.common.issues.push(s)}class ft{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return ye;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const o=await a.key,i=await a.value;s.push({key:o,value:i})}return ft.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:o,value:i}=a;if(o.status==="aborted"||i.status==="aborted")return ye;o.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),o.value==="__proto__"||i.value===void 0&&!a.alwaysSet||(s[o.value]=i.value)}return{status:e.value,value:s}}}const ye=Object.freeze({status:"aborted"}),rr=r=>({status:"dirty",value:r}),_t=r=>({status:"valid",value:r}),xr=r=>r.status==="aborted",Mr=r=>r.status==="dirty",ns=r=>r.status==="valid",Is=r=>typeof Promise<"u"&&r instanceof Promise;function ar(r,e,t,s){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function ma(r,e,t,s,a){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var ne,xs,Ms;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(ne||(ne={}));class Zt{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Hr=(r,e)=>{if(ns(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new bt(r.common.issues);return this._error=t,this._error}}};function Re(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(o,i)=>{var d,c;const{message:u}=r;return o.code==="invalid_enum_value"?{message:u??i.defaultError}:i.data===void 0?{message:(d=u??s)!==null&&d!==void 0?d:i.defaultError}:o.code!=="invalid_type"?{message:i.defaultError}:{message:(c=u??t)!==null&&c!==void 0?c:i.defaultError}},description:a}}class Le{get description(){return this._def.description}_getType(e){return jt(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:jt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ft,ctx:{common:e.parent.common,data:e.data,parsedType:jt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Is(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const a={common:{issues:[],async:(s=t==null?void 0:t.async)!==null&&s!==void 0&&s,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:jt(e)},o=this._parseSync({data:e,path:a.path,parent:a});return Hr(a,o)}"~validate"(e){var t,s;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:jt(e)};if(!this["~standard"].async)try{const o=this._parseSync({data:e,path:[],parent:a});return ns(o)?{value:o.value}:{issues:a.common.issues}}catch(o){!((s=(t=o==null?void 0:o.message)===null||t===void 0?void 0:t.toLowerCase())===null||s===void 0)&&s.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then(o=>ns(o)?{value:o.value}:{issues:a.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:jt(e)},a=this._parse({data:e,path:s.path,parent:s}),o=await(Is(a)?a:Promise.resolve(a));return Hr(s,o)}refine(e,t){const s=a=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,o)=>{const i=e(a),d=()=>o.addIssue({code:S.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(d(),!1)):!!i||(d(),!1)})}refinement(e,t){return this._refinement((s,a)=>!!e(s)||(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new Nt({schema:this,typeName:fe.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return Lt.create(this,this._def)}nullable(){return Xt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Rt.create(this)}promise(){return ys.create(this,this._def)}or(e){return Zs.create([this,e],this._def)}and(e){return Os.create(this,e,this._def)}transform(e){return new Nt({...Re(this._def),schema:this,typeName:fe.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Ds({...Re(this._def),innerType:this,defaultValue:t,typeName:fe.ZodDefault})}brand(){return new Lr({typeName:fe.ZodBranded,type:this,...Re(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Us({...Re(this._def),innerType:this,catchValue:t,typeName:fe.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Bs.create(this,e)}readonly(){return Vs.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Ln=/^c[^\s-]{8,}$/i,zn=/^[0-9a-z]+$/,Zn=/^[0-9A-HJKMNP-TV-Z]{26}$/i,On=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Pn=/^[a-z0-9_-]{21}$/i,Fn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,jn=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Dn=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let yr;const Un=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Vn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,qn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Hn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Bn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Gn=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ga="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Jn=new RegExp(`^${ga}$`);function fa(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function ya(r){let e=`${ga}T${fa(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Wn(r,e){if(!Fn.test(r))return!1;try{const[t]=r.split("."),s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return typeof a=="object"&&a!==null&&!(!a.typ||!a.alg)&&(!e||a.alg===e)}catch{return!1}}function Kn(r,e){return!(e!=="v4"&&e||!Vn.test(r))||!(e!=="v6"&&e||!Hn.test(r))}class Tt extends Le{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==B.string){const i=this._getOrReturnCtx(e);return P(i,{code:S.invalid_type,expected:B.string,received:i.parsedType}),ye}const t=new ft;let s;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),P(s,{code:S.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),P(s,{code:S.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const d=e.data.length>i.value,c=e.data.length<i.value;(d||c)&&(s=this._getOrReturnCtx(e,s),d?P(s,{code:S.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&P(s,{code:S.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Dn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"email",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")yr||(yr=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),yr.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"emoji",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")On.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"uuid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")Pn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"nanoid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")Ln.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"cuid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")zn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"cuid2",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Zn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"ulid",code:S.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),P(s,{validation:"url",code:S.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"regex",code:S.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),P(s,{code:S.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),P(s,{code:S.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),P(s,{code:S.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?ya(i).test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{code:S.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Jn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{code:S.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${fa(i)}$`).test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{code:S.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?jn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"duration",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(a=e.data,((o=i.version)!=="v4"&&o||!Un.test(a))&&(o!=="v6"&&o||!qn.test(a))&&(s=this._getOrReturnCtx(e,s),P(s,{validation:"ip",code:S.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Wn(e.data,i.alg)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"jwt",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Kn(e.data,i.version)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"cidr",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Bn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"base64",code:S.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Gn.test(e.data)||(s=this._getOrReturnCtx(e,s),P(s,{validation:"base64url",code:S.invalid_string,message:i.message}),t.dirty()):qe.assertNever(i);var a,o;return{status:t.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:S.invalid_string,...ne.errToObj(s)})}_addCheck(e){return new Tt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...ne.errToObj(e)})}url(e){return this._addCheck({kind:"url",...ne.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...ne.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...ne.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...ne.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...ne.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...ne.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...ne.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...ne.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...ne.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...ne.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...ne.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...ne.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(s=e==null?void 0:e.local)!==null&&s!==void 0&&s,...ne.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...ne.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...ne.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...ne.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...ne.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...ne.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...ne.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...ne.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...ne.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...ne.errToObj(t)})}nonempty(e){return this.min(1,ne.errToObj(e))}trim(){return new Tt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Tt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Tt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Yn(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s;return parseInt(r.toFixed(a).replace(".",""))%parseInt(e.toFixed(a).replace(".",""))/Math.pow(10,a)}Tt.create=r=>{var e;return new Tt({checks:[],typeName:fe.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Re(r)})};class Wt extends Le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==B.number){const a=this._getOrReturnCtx(e);return P(a,{code:S.invalid_type,expected:B.number,received:a.parsedType}),ye}let t;const s=new ft;for(const a of this._def.checks)a.kind==="int"?qe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),P(t,{code:S.invalid_type,expected:"integer",received:"float",message:a.message}),s.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:S.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:S.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="multipleOf"?Yn(e.data,a.value)!==0&&(t=this._getOrReturnCtx(e,t),P(t,{code:S.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),P(t,{code:S.not_finite,message:a.message}),s.dirty()):qe.assertNever(a);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,ne.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ne.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ne.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ne.toString(t))}setLimit(e,t,s,a){return new Wt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ne.toString(a)}]})}_addCheck(e){return new Wt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:ne.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ne.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ne.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ne.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ne.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ne.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:ne.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ne.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ne.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&qe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Wt.create=r=>new Wt({checks:[],typeName:fe.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...Re(r)});class Kt extends Le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==B.bigint)return this._getInvalidInput(e);let t;const s=new ft;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:S.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:S.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),P(t,{code:S.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):qe.assertNever(a);return{status:s.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.bigint,received:t.parsedType}),ye}gte(e,t){return this.setLimit("min",e,!0,ne.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ne.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ne.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ne.toString(t))}setLimit(e,t,s,a){return new Kt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ne.toString(a)}]})}_addCheck(e){return new Kt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ne.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ne.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ne.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ne.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ne.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Kt.create=r=>{var e;return new Kt({checks:[],typeName:fe.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Re(r)})};class Es extends Le{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==B.boolean){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.boolean,received:t.parsedType}),ye}return _t(e.data)}}Es.create=r=>new Es({typeName:fe.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...Re(r)});class is extends Le{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==B.date){const a=this._getOrReturnCtx(e);return P(a,{code:S.invalid_type,expected:B.date,received:a.parsedType}),ye}if(isNaN(e.data.getTime()))return P(this._getOrReturnCtx(e),{code:S.invalid_date}),ye;const t=new ft;let s;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(s=this._getOrReturnCtx(e,s),P(s,{code:S.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(s=this._getOrReturnCtx(e,s),P(s,{code:S.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):qe.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new is({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:ne.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:ne.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}is.create=r=>new is({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:fe.ZodDate,...Re(r)});class nr extends Le{_parse(e){if(this._getType(e)!==B.symbol){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.symbol,received:t.parsedType}),ye}return _t(e.data)}}nr.create=r=>new nr({typeName:fe.ZodSymbol,...Re(r)});class Ls extends Le{_parse(e){if(this._getType(e)!==B.undefined){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.undefined,received:t.parsedType}),ye}return _t(e.data)}}Ls.create=r=>new Ls({typeName:fe.ZodUndefined,...Re(r)});class zs extends Le{_parse(e){if(this._getType(e)!==B.null){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.null,received:t.parsedType}),ye}return _t(e.data)}}zs.create=r=>new zs({typeName:fe.ZodNull,...Re(r)});class fs extends Le{constructor(){super(...arguments),this._any=!0}_parse(e){return _t(e.data)}}fs.create=r=>new fs({typeName:fe.ZodAny,...Re(r)});class ss extends Le{constructor(){super(...arguments),this._unknown=!0}_parse(e){return _t(e.data)}}ss.create=r=>new ss({typeName:fe.ZodUnknown,...Re(r)});class Vt extends Le{_parse(e){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.never,received:t.parsedType}),ye}}Vt.create=r=>new Vt({typeName:fe.ZodNever,...Re(r)});class ir extends Le{_parse(e){if(this._getType(e)!==B.undefined){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.void,received:t.parsedType}),ye}return _t(e.data)}}ir.create=r=>new ir({typeName:fe.ZodVoid,...Re(r)});class Rt extends Le{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==B.array)return P(t,{code:S.invalid_type,expected:B.array,received:t.parsedType}),ye;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,d=t.data.length<a.exactLength.value;(i||d)&&(P(t,{code:i?S.too_big:S.too_small,minimum:d?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(P(t,{code:S.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(P(t,{code:S.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,d)=>a.type._parseAsync(new Zt(t,i,t.path,d)))).then(i=>ft.mergeArray(s,i));const o=[...t.data].map((i,d)=>a.type._parseSync(new Zt(t,i,t.path,d)));return ft.mergeArray(s,o)}get element(){return this._def.type}min(e,t){return new Rt({...this._def,minLength:{value:e,message:ne.toString(t)}})}max(e,t){return new Rt({...this._def,maxLength:{value:e,message:ne.toString(t)}})}length(e,t){return new Rt({...this._def,exactLength:{value:e,message:ne.toString(t)}})}nonempty(e){return this.min(1,e)}}function ds(r){if(r instanceof et){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=Lt.create(ds(s))}return new et({...r._def,shape:()=>e})}return r instanceof Rt?new Rt({...r._def,type:ds(r.element)}):r instanceof Lt?Lt.create(ds(r.unwrap())):r instanceof Xt?Xt.create(ds(r.unwrap())):r instanceof Ot?Ot.create(r.items.map(e=>ds(e))):r}Rt.create=(r,e)=>new Rt({type:r,minLength:null,maxLength:null,exactLength:null,typeName:fe.ZodArray,...Re(e)});class et extends Le{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=qe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==B.object){const c=this._getOrReturnCtx(e);return P(c,{code:S.invalid_type,expected:B.object,received:c.parsedType}),ye}const{status:t,ctx:s}=this._processInputParams(e),{shape:a,keys:o}=this._getCached(),i=[];if(!(this._def.catchall instanceof Vt&&this._def.unknownKeys==="strip"))for(const c in s.data)o.includes(c)||i.push(c);const d=[];for(const c of o){const u=a[c],p=s.data[c];d.push({key:{status:"valid",value:c},value:u._parse(new Zt(s,p,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof Vt){const c=this._def.unknownKeys;if(c==="passthrough")for(const u of i)d.push({key:{status:"valid",value:u},value:{status:"valid",value:s.data[u]}});else if(c==="strict")i.length>0&&(P(s,{code:S.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const u of i){const p=s.data[u];d.push({key:{status:"valid",value:u},value:c._parse(new Zt(s,p,s.path,u)),alwaysSet:u in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const u of d){const p=await u.key,pe=await u.value;c.push({key:p,value:pe,alwaysSet:u.alwaysSet})}return c}).then(c=>ft.mergeObjectSync(t,c)):ft.mergeObjectSync(t,d)}get shape(){return this._def.shape()}strict(e){return ne.errToObj,new et({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var a,o,i,d;const c=(i=(o=(a=this._def).errorMap)===null||o===void 0?void 0:o.call(a,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(d=ne.errToObj(e).message)!==null&&d!==void 0?d:c}:{message:c}}}:{}})}strip(){return new et({...this._def,unknownKeys:"strip"})}passthrough(){return new et({...this._def,unknownKeys:"passthrough"})}extend(e){return new et({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new et({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:fe.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new et({...this._def,catchall:e})}pick(e){const t={};return qe.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new et({...this._def,shape:()=>t})}omit(e){const t={};return qe.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new et({...this._def,shape:()=>t})}deepPartial(){return ds(this)}partial(e){const t={};return qe.objectKeys(this.shape).forEach(s=>{const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}),new et({...this._def,shape:()=>t})}required(e){const t={};return qe.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof Lt;)a=a._def.innerType;t[s]=a}}),new et({...this._def,shape:()=>t})}keyof(){return _a(qe.objectKeys(this.shape))}}et.create=(r,e)=>new et({shape:()=>r,unknownKeys:"strip",catchall:Vt.create(),typeName:fe.ZodObject,...Re(e)}),et.strictCreate=(r,e)=>new et({shape:()=>r,unknownKeys:"strict",catchall:Vt.create(),typeName:fe.ZodObject,...Re(e)}),et.lazycreate=(r,e)=>new et({shape:r,unknownKeys:"strip",catchall:Vt.create(),typeName:fe.ZodObject,...Re(e)});class Zs extends Le{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map(async a=>{const o={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:o}),ctx:o}})).then(function(a){for(const i of a)if(i.result.status==="valid")return i.result;for(const i of a)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const o=a.map(i=>new bt(i.ctx.common.issues));return P(t,{code:S.invalid_union,unionErrors:o}),ye});{let a;const o=[];for(const d of s){const c={...t,common:{...t.common,issues:[]},parent:null},u=d._parseSync({data:t.data,path:t.path,parent:c});if(u.status==="valid")return u;u.status!=="dirty"||a||(a={result:u,ctx:c}),c.common.issues.length&&o.push(c.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const i=o.map(d=>new bt(d));return P(t,{code:S.invalid_union,unionErrors:i}),ye}}get options(){return this._def.options}}Zs.create=(r,e)=>new Zs({options:r,typeName:fe.ZodUnion,...Re(e)});const Bt=r=>r instanceof Ps?Bt(r.schema):r instanceof Nt?Bt(r.innerType()):r instanceof Fs?[r.value]:r instanceof Yt?r.options:r instanceof js?qe.objectValues(r.enum):r instanceof Ds?Bt(r._def.innerType):r instanceof Ls?[void 0]:r instanceof zs?[null]:r instanceof Lt?[void 0,...Bt(r.unwrap())]:r instanceof Xt?[null,...Bt(r.unwrap())]:r instanceof Lr||r instanceof Vs?Bt(r.unwrap()):r instanceof Us?Bt(r._def.innerType):[];class dr extends Le{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.object)return P(t,{code:S.invalid_type,expected:B.object,received:t.parsedType}),ye;const s=this.discriminator,a=t.data[s],o=this.optionsMap.get(a);return o?t.common.async?o._parseAsync({data:t.data,path:t.path,parent:t}):o._parseSync({data:t.data,path:t.path,parent:t}):(P(t,{code:S.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),ye)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){const a=new Map;for(const o of t){const i=Bt(o.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const d of i){if(a.has(d))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(d)}`);a.set(d,o)}}return new dr({typeName:fe.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...Re(s)})}}function Ar(r,e){const t=jt(r),s=jt(e);if(r===e)return{valid:!0,data:r};if(t===B.object&&s===B.object){const a=qe.objectKeys(e),o=qe.objectKeys(r).filter(d=>a.indexOf(d)!==-1),i={...r,...e};for(const d of o){const c=Ar(r[d],e[d]);if(!c.valid)return{valid:!1};i[d]=c.data}return{valid:!0,data:i}}if(t===B.array&&s===B.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let o=0;o<r.length;o++){const i=Ar(r[o],e[o]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}return t===B.date&&s===B.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class Os extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(o,i)=>{if(xr(o)||xr(i))return ye;const d=Ar(o.value,i.value);return d.valid?((Mr(o)||Mr(i))&&t.dirty(),{status:t.value,value:d.data}):(P(s,{code:S.invalid_intersection_types}),ye)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([o,i])=>a(o,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}Os.create=(r,e,t)=>new Os({left:r,right:e,typeName:fe.ZodIntersection,...Re(t)});class Ot extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.array)return P(s,{code:S.invalid_type,expected:B.array,received:s.parsedType}),ye;if(s.data.length<this._def.items.length)return P(s,{code:S.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),ye;!this._def.rest&&s.data.length>this._def.items.length&&(P(s,{code:S.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...s.data].map((o,i)=>{const d=this._def.items[i]||this._def.rest;return d?d._parse(new Zt(s,o,s.path,i)):null}).filter(o=>!!o);return s.common.async?Promise.all(a).then(o=>ft.mergeArray(t,o)):ft.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new Ot({...this._def,rest:e})}}Ot.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ot({items:r,typeName:fe.ZodTuple,rest:null,...Re(e)})};class cr extends Le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.object)return P(s,{code:S.invalid_type,expected:B.object,received:s.parsedType}),ye;const a=[],o=this._def.keyType,i=this._def.valueType;for(const d in s.data)a.push({key:o._parse(new Zt(s,d,s.path,d)),value:i._parse(new Zt(s,s.data[d],s.path,d)),alwaysSet:d in s.data});return s.common.async?ft.mergeObjectAsync(t,a):ft.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,s){return new cr(t instanceof Le?{keyType:e,valueType:t,typeName:fe.ZodRecord,...Re(s)}:{keyType:Tt.create(),valueType:e,typeName:fe.ZodRecord,...Re(t)})}}class or extends Le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.map)return P(s,{code:S.invalid_type,expected:B.map,received:s.parsedType}),ye;const a=this._def.keyType,o=this._def.valueType,i=[...s.data.entries()].map(([d,c],u)=>({key:a._parse(new Zt(s,d,s.path,[u,"key"])),value:o._parse(new Zt(s,c,s.path,[u,"value"]))}));if(s.common.async){const d=new Map;return Promise.resolve().then(async()=>{for(const c of i){const u=await c.key,p=await c.value;if(u.status==="aborted"||p.status==="aborted")return ye;u.status!=="dirty"&&p.status!=="dirty"||t.dirty(),d.set(u.value,p.value)}return{status:t.value,value:d}})}{const d=new Map;for(const c of i){const u=c.key,p=c.value;if(u.status==="aborted"||p.status==="aborted")return ye;u.status!=="dirty"&&p.status!=="dirty"||t.dirty(),d.set(u.value,p.value)}return{status:t.value,value:d}}}}or.create=(r,e,t)=>new or({valueType:e,keyType:r,typeName:fe.ZodMap,...Re(t)});class os extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==B.set)return P(s,{code:S.invalid_type,expected:B.set,received:s.parsedType}),ye;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(P(s,{code:S.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(P(s,{code:S.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const o=this._def.valueType;function i(c){const u=new Set;for(const p of c){if(p.status==="aborted")return ye;p.status==="dirty"&&t.dirty(),u.add(p.value)}return{status:t.value,value:u}}const d=[...s.data.values()].map((c,u)=>o._parse(new Zt(s,c,s.path,u)));return s.common.async?Promise.all(d).then(c=>i(c)):i(d)}min(e,t){return new os({...this._def,minSize:{value:e,message:ne.toString(t)}})}max(e,t){return new os({...this._def,maxSize:{value:e,message:ne.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}os.create=(r,e)=>new os({valueType:r,minSize:null,maxSize:null,typeName:fe.ZodSet,...Re(e)});class vs extends Le{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.function)return P(t,{code:S.invalid_type,expected:B.function,received:t.parsedType}),ye;function s(d,c){return sr({data:d,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,tr(),gs].filter(u=>!!u),issueData:{code:S.invalid_arguments,argumentsError:c}})}function a(d,c){return sr({data:d,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,tr(),gs].filter(u=>!!u),issueData:{code:S.invalid_return_type,returnTypeError:c}})}const o={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof ys){const d=this;return _t(async function(...c){const u=new bt([]),p=await d._def.args.parseAsync(c,o).catch(N=>{throw u.addIssue(s(c,N)),u}),pe=await Reflect.apply(i,this,p);return await d._def.returns._def.type.parseAsync(pe,o).catch(N=>{throw u.addIssue(a(pe,N)),u})})}{const d=this;return _t(function(...c){const u=d._def.args.safeParse(c,o);if(!u.success)throw new bt([s(c,u.error)]);const p=Reflect.apply(i,this,u.data),pe=d._def.returns.safeParse(p,o);if(!pe.success)throw new bt([a(p,pe.error)]);return pe.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new vs({...this._def,args:Ot.create(e).rest(ss.create())})}returns(e){return new vs({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new vs({args:e||Ot.create([]).rest(ss.create()),returns:t||ss.create(),typeName:fe.ZodFunction,...Re(s)})}}class Ps extends Le{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Ps.create=(r,e)=>new Ps({getter:r,typeName:fe.ZodLazy,...Re(e)});class Fs extends Le{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return P(t,{received:t.data,code:S.invalid_literal,expected:this._def.value}),ye}return{status:"valid",value:e.data}}get value(){return this._def.value}}function _a(r,e){return new Yt({values:r,typeName:fe.ZodEnum,...Re(e)})}Fs.create=(r,e)=>new Fs({value:r,typeName:fe.ZodLiteral,...Re(e)});class Yt extends Le{constructor(){super(...arguments),xs.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return P(t,{expected:qe.joinValues(s),received:t.parsedType,code:S.invalid_type}),ye}if(ar(this,xs)||ma(this,xs,new Set(this._def.values)),!ar(this,xs).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return P(t,{received:t.data,code:S.invalid_enum_value,options:s}),ye}return _t(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Yt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Yt.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}xs=new WeakMap,Yt.create=_a;class js extends Le{constructor(){super(...arguments),Ms.set(this,void 0)}_parse(e){const t=qe.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==B.string&&s.parsedType!==B.number){const a=qe.objectValues(t);return P(s,{expected:qe.joinValues(a),received:s.parsedType,code:S.invalid_type}),ye}if(ar(this,Ms)||ma(this,Ms,new Set(qe.getValidEnumValues(this._def.values))),!ar(this,Ms).has(e.data)){const a=qe.objectValues(t);return P(s,{received:s.data,code:S.invalid_enum_value,options:a}),ye}return _t(e.data)}get enum(){return this._def.values}}Ms=new WeakMap,js.create=(r,e)=>new js({values:r,typeName:fe.ZodNativeEnum,...Re(e)});class ys extends Le{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==B.promise&&t.common.async===!1)return P(t,{code:S.invalid_type,expected:B.promise,received:t.parsedType}),ye;const s=t.parsedType===B.promise?t.data:Promise.resolve(t.data);return _t(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}ys.create=(r,e)=>new ys({type:r,typeName:fe.ZodPromise,...Re(e)});class Nt extends Le{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===fe.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,o={addIssue:i=>{P(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(o.addIssue=o.addIssue.bind(o),a.type==="preprocess"){const i=a.transform(s.data,o);if(s.common.async)return Promise.resolve(i).then(async d=>{if(t.value==="aborted")return ye;const c=await this._def.schema._parseAsync({data:d,path:s.path,parent:s});return c.status==="aborted"?ye:c.status==="dirty"||t.value==="dirty"?rr(c.value):c});{if(t.value==="aborted")return ye;const d=this._def.schema._parseSync({data:i,path:s.path,parent:s});return d.status==="aborted"?ye:d.status==="dirty"||t.value==="dirty"?rr(d.value):d}}if(a.type==="refinement"){const i=d=>{const c=a.refinement(d,o);if(s.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return d};if(s.common.async===!1){const d=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return d.status==="aborted"?ye:(d.status==="dirty"&&t.dirty(),i(d.value),{status:t.value,value:d.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(d=>d.status==="aborted"?ye:(d.status==="dirty"&&t.dirty(),i(d.value).then(()=>({status:t.value,value:d.value}))))}if(a.type==="transform"){if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!ns(i))return i;const d=a.transform(i.value,o);if(d instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:d}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>ns(i)?Promise.resolve(a.transform(i.value,o)).then(d=>({status:t.value,value:d})):i)}qe.assertNever(a)}}Nt.create=(r,e,t)=>new Nt({schema:r,typeName:fe.ZodEffects,effect:e,...Re(t)}),Nt.createWithPreprocess=(r,e,t)=>new Nt({schema:e,effect:{type:"preprocess",transform:r},typeName:fe.ZodEffects,...Re(t)});class Lt extends Le{_parse(e){return this._getType(e)===B.undefined?_t(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Lt.create=(r,e)=>new Lt({innerType:r,typeName:fe.ZodOptional,...Re(e)});class Xt extends Le{_parse(e){return this._getType(e)===B.null?_t(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Xt.create=(r,e)=>new Xt({innerType:r,typeName:fe.ZodNullable,...Re(e)});class Ds extends Le{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===B.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Ds.create=(r,e)=>new Ds({innerType:r,typeName:fe.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Re(e)});class Us extends Le{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return Is(a)?a.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new bt(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new bt(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}Us.create=(r,e)=>new Us({innerType:r,typeName:fe.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Re(e)});class lr extends Le{_parse(e){if(this._getType(e)!==B.nan){const t=this._getOrReturnCtx(e);return P(t,{code:S.invalid_type,expected:B.nan,received:t.parsedType}),ye}return{status:"valid",value:e.data}}}lr.create=r=>new lr({typeName:fe.ZodNaN,...Re(r)});const Xn=Symbol("zod_brand");class Lr extends Le{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class Bs extends Le{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?ye:a.status==="dirty"?(t.dirty(),rr(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?ye:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new Bs({in:e,out:t,typeName:fe.ZodPipeline})}}class Vs extends Le{_parse(e){const t=this._def.innerType._parse(e),s=a=>(ns(a)&&(a.value=Object.freeze(a.value)),a);return Is(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}function Br(r,e={},t){return r?fs.create().superRefine((s,a)=>{var o,i;if(!r(s)){const d=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,c=(i=(o=d.fatal)!==null&&o!==void 0?o:t)===null||i===void 0||i,u=typeof d=="string"?{message:d}:d;a.addIssue({code:"custom",...u,fatal:c})}}):fs.create()}Vs.create=(r,e)=>new Vs({innerType:r,typeName:fe.ZodReadonly,...Re(e)});const Qn={object:et.lazycreate};var fe;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(fe||(fe={}));const Gr=Tt.create,Jr=Wt.create,ei=lr.create,ti=Kt.create,Wr=Es.create,si=is.create,ri=nr.create,ai=Ls.create,ni=zs.create,ii=fs.create,oi=ss.create,li=Vt.create,di=ir.create,ci=Rt.create,ui=et.create,hi=et.strictCreate,pi=Zs.create,vi=dr.create,mi=Os.create,gi=Ot.create,fi=cr.create,yi=or.create,_i=os.create,wi=vs.create,Ci=Ps.create,bi=Fs.create,$i=Yt.create,Si=js.create,ki=ys.create,Kr=Nt.create,xi=Lt.create,Mi=Xt.create,Ai=Nt.createWithPreprocess,Ti=Bs.create,Ni={string:r=>Tt.create({...r,coerce:!0}),number:r=>Wt.create({...r,coerce:!0}),boolean:r=>Es.create({...r,coerce:!0}),bigint:r=>Kt.create({...r,coerce:!0}),date:r=>is.create({...r,coerce:!0})},Ri=ye;var He=Object.freeze({__proto__:null,defaultErrorMap:gs,setErrorMap:function(r){va=r},getErrorMap:tr,makeIssue:sr,EMPTY_PATH:[],addIssueToContext:P,ParseStatus:ft,INVALID:ye,DIRTY:rr,OK:_t,isAborted:xr,isDirty:Mr,isValid:ns,isAsync:Is,get util(){return qe},get objectUtil(){return kr},ZodParsedType:B,getParsedType:jt,ZodType:Le,datetimeRegex:ya,ZodString:Tt,ZodNumber:Wt,ZodBigInt:Kt,ZodBoolean:Es,ZodDate:is,ZodSymbol:nr,ZodUndefined:Ls,ZodNull:zs,ZodAny:fs,ZodUnknown:ss,ZodNever:Vt,ZodVoid:ir,ZodArray:Rt,ZodObject:et,ZodUnion:Zs,ZodDiscriminatedUnion:dr,ZodIntersection:Os,ZodTuple:Ot,ZodRecord:cr,ZodMap:or,ZodSet:os,ZodFunction:vs,ZodLazy:Ps,ZodLiteral:Fs,ZodEnum:Yt,ZodNativeEnum:js,ZodPromise:ys,ZodEffects:Nt,ZodTransformer:Nt,ZodOptional:Lt,ZodNullable:Xt,ZodDefault:Ds,ZodCatch:Us,ZodNaN:lr,BRAND:Xn,ZodBranded:Lr,ZodPipeline:Bs,ZodReadonly:Vs,custom:Br,Schema:Le,ZodSchema:Le,late:Qn,get ZodFirstPartyTypeKind(){return fe},coerce:Ni,any:ii,array:ci,bigint:ti,boolean:Wr,date:si,discriminatedUnion:vi,effect:Kr,enum:$i,function:wi,instanceof:(r,e={message:`Input not instance of ${r.name}`})=>Br(t=>t instanceof r,e),intersection:mi,lazy:Ci,literal:bi,map:yi,nan:ei,nativeEnum:Si,never:li,null:ni,nullable:Mi,number:Jr,object:ui,oboolean:()=>Wr().optional(),onumber:()=>Jr().optional(),optional:xi,ostring:()=>Gr().optional(),pipeline:Ti,preprocess:Ai,promise:ki,record:fi,set:_i,strictObject:hi,string:Gr,symbol:ri,transformer:Kr,tuple:gi,undefined:ai,union:pi,unknown:oi,void:di,NEVER:Ri,ZodIssueCode:S,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:bt});class gt extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,gt.prototype)}}const Ut=He.object({name:He.string().optional(),title:He.string().optional(),type:He.enum(["stdio","http","sse"]).optional(),command:He.string().optional(),args:He.array(He.union([He.string(),He.number(),He.boolean()])).optional(),env:He.record(He.union([He.string(),He.number(),He.boolean(),He.null(),He.undefined()])).optional(),url:He.string().optional()}).passthrough();function kt(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function cs(r){return(r==null?void 0:r.type)==="stdio"}function Ks(r){return kt(r)?r.url:cs(r)?r.command:""}const Ii=He.array(Ut),Ei=He.object({servers:He.array(Ut)}),Li=He.object({mcpServers:He.array(Ut)}),zi=He.object({servers:He.record(He.unknown())}),Zi=He.object({mcpServers:He.record(He.unknown())}),Oi=He.record(He.unknown()),Pi=Ut.refine(r=>{const e=r.command!==void 0,t=r.url!==void 0;if(!e&&!t)return!1;const s=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(a=>s.has(a))},{message:"Single server object must have valid server properties"});function Qt(r){try{const e=Ut.transform(t=>{let s;if(t.type)s=t.type;else if(t.url)s="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");s="stdio"}if(s==="http"||s==="sse"){if(!t.url)throw new Error(`${s.toUpperCase()} server must have a 'url' property`);return{type:s,name:t.name||t.title||t.url,url:t.url}}{const a=t.command||"",o=t.args?t.args.map(u=>String(u)):[];if(!a)throw new Error("Stdio server must have a 'command' property");const i=o.length>0?`${a} ${o.join(" ")}`:a,d=t.name||t.title||(a?a.split(" ")[0]:""),c=t.env?Object.fromEntries(Object.entries(t.env).filter(([u,p])=>p!=null).map(([u,p])=>[u,String(p)])):void 0;return{type:"stdio",name:d,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!e.success)throw new gt(e.error.message);return e.data}catch(e){throw e instanceof Error?new gt(`Invalid server configuration: ${e.message}`):new gt("Invalid server configuration")}}class Gs{constructor(e){je(this,"servers",vt([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===Je.getStoredMCPServersResponse){const s=t.data;return Array.isArray(s)&&this.servers.set(s),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:Je.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:Je.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new gt("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const s=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(s),s})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const s=[...t,...e.map(a=>({...a,id:crypto.randomUUID()}))];return this.saveServers(s),s})}checkExistingServerName(e,t){const s=_r(this.servers).find(a=>a.name===e);if(s&&(s==null?void 0:s.id)!==t)throw new gt(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const s=t.map(a=>a.id===e.id?e:a);return this.saveServers(s),s})}deleteServer(e){this.servers.update(t=>{const s=t.filter(a=>a.id!==e.id);return this.saveServers(s),s}),e.type==="http"&&e.authRequired&&this.host.postMessage({type:Je.deleteOAuthSession,data:e.name})}toggleDisabledServer(e){this.servers.update(t=>{const s=t.map(a=>a.id===e?{...a,disabled:!a.disabled}:a);return this.saveServers(s),s})}static convertServerToJSON(e){if(kt(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,s=new Map;e.forEach(o=>{var d,c;const i=(d=o.tools)==null?void 0:d.filter(u=>!u.enabled).map(u=>u.definition.mcp_tool_name);o.disabled?t.set(o.id,"MCP server has been manually disabled"):o.tools&&o.tools.length===0?t.set(o.id,"No tools are available for this MCP server"):i&&i.length===((c=o.tools)==null?void 0:c.length)?t.set(o.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&s.set(o.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const a=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...a]),warnings:s}}static parseDuplicateServerIds(e){const t=new Map;for(const a of e)t.has(a.name)||t.set(a.name,[]),t.get(a.name).push(a.id);const s=new Map;for(const[,a]of t)if(a.length>1)for(let o=1;o<a.length;o++)s.set(a[o],"MCP server is disabled due to duplicate server names");return s}static convertParsedServerToWebview(e){const{tools:t,...s}=e;return{...s,tools:void 0}}static parseServerConfigFromJSON(e){return function(s){try{const a=JSON.parse(s),o=He.union([Ii.transform(i=>i.map(d=>Qt(d))),Ei.transform(i=>i.servers.map(d=>Qt(d))),Li.transform(i=>i.mcpServers.map(d=>Qt(d))),zi.transform(i=>Object.entries(i.servers).map(([d,c])=>{const u=Ut.parse(c);return Qt({...u,name:u.name||d})})),Zi.transform(i=>Object.entries(i.mcpServers).map(([d,c])=>{const u=Ut.parse(c);return Qt({...u,name:u.name||d})})),Pi.transform(i=>[Qt(i)]),Oi.transform(i=>{if(!Object.values(i).some(d=>{const c=Ut.safeParse(d);return c.success&&(c.data.command!==void 0||c.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(i).map(([d,c])=>{const u=Ut.parse(c);return Qt({...u,name:u.name||d})})})]).safeParse(a);if(o.success)return o.data;throw new gt("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(a){throw a instanceof gt?a:new gt("Failed to parse MCP servers from JSON. Please check the format.")}}(e).map(s=>this.convertParsedServerToWebview(s))}importFromJSON(e){try{const t=Gs.parseServerConfigFromJSON(e),s=_r(this.servers),a=new Set(s.map(o=>o.name));for(const o of t){if(!o.name)throw new gt("All servers must have a name.");if(a.has(o.name))throw new gt(`A server with the name '${o.name}' already exists.`);a.add(o.name)}return this.servers.update(o=>{const i=[...o,...t.map(d=>({...d,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof gt?t:new gt("Failed to import MCP servers from JSON. Please check the format.")}}}class Fi{constructor(e){je(this,"_terminalSettings",vt({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===Je.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:Je.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:Je.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:Je.updateTerminalSettings,data:{startupScript:e}})}}const As=class As{constructor(e){je(this,"_swarmModeSettings",vt(Ws));je(this,"_isLoaded",!1);je(this,"_pollInterval",null);je(this,"_lastKnownSettingsHash","");je(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:gr.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(Ws),this._lastKnownSettingsHash=JSON.stringify(Ws),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:gr.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(Ws)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},As.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:gr.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};je(As,"key","swarmModeModel"),je(As,"POLLING_INTERVAL_MS",5e3);let qs=As;var It=(r=>(r.file="file",r.folder="folder",r))(It||{});class Jt{constructor(e,t){je(this,"subscribe");je(this,"set");je(this,"update");je(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case Je.wsContextSourceFoldersChanged:case Je.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case Je.sourceFoldersSyncStatus:this.update(s=>({...s,syncStatus:t.data.status}))}});je(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:Je.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);je(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:Je.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,s)=>t.type===s.type?t.name.localeCompare(s.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:s,set:a,update:o}=vt({sourceFolders:[],sourceTree:[],syncStatus:wr.done});this.subscribe=s,this.set=a,this.update=o,this.getSourceFolders().then(i=>{this.update(d=>({...d,sourceFolders:i,sourceTree:Jt.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==St.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:Je.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:Je.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:Je.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=_r(this);const s=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(a=>({...a,sourceFolders:e,sourceTree:s}))}async getRefreshedSourceTree(e,t){const s=Jt.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,s)}async getRefreshedSourceTreeRecurse(e,t){const s=new Map(e.map(a=>[JSON.stringify([a.fileId.folderRoot,a.fileId.relPath]),a]));for(let a of t){const o=Jt.fileIdToString(a.fileId);if(a.type==="folder"){const i=s.get(o);i&&(a.expanded=i.type==="folder"&&i.expanded,a.expanded&&(a.children=await this.getChildren(a.fileId),a.children=await this.getRefreshedSourceTreeRecurse(i.children,a.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,s)=>t.name.localeCompare(s.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}var ji=g('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),Di=g('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');const Ui="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",Vi="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",qi="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var Hi=g('<div class="children-container"></div>'),Bi=g('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function wa(r,e){st(e,!1);let t=b(e,"data",8),s=b(e,"wsContextModel",8),a=b(e,"indentLevel",8);const o=()=>{s().toggleNode(t())},i={[St.included]:Ui,[St.excluded]:Vi,[St.partial]:qi},d={[St.included]:"included",[St.excluded]:"excluded",[St.partial]:"partially included"};let c=F(),u=F(),p=F();me(()=>w(t()),()=>{var E;m(u,(E=t()).type===It.folder&&E.inclusionState!==St.excluded?E.expanded?"chevron-down":"chevron-right":E.type===It.folder?"folder":"file")}),me(()=>(w(t()),St),()=>{m(c,t().type===It.folder&&t().inclusionState!==St.excluded)}),me(()=>(w(t()),It),()=>{m(p,t().type===It.folder&&t().expanded&&t().children&&t().children.length>0?t():null)}),ht(),at();var pe=Bi(),N=h(pe),he=hs(()=>Xs("Enter",o));let we;var D=h(N);da(D,{get icon(){return n(u)}});var I=v(D,2),O=h(I),re=v(I,2),K=E=>{de(E,{size:1,class:"file-count",children:(x,M)=>{var G=R();ge(L=>Ze(G,L),[()=>(w(t()),f(()=>t().trackedFileCount.toLocaleString()))],xe),l(x,G)},$$slots:{default:!0}})};V(re,E=>{w(t()),w(It),w(St),f(()=>t().type===It.folder&&t().inclusionState!==St.excluded&&typeof t().trackedFileCount=="number")&&E(K)});var A=v(re,2),C=v(N,2),j=E=>{var x=Hi();ut(x,5,()=>(n(p),f(()=>n(p).children)),M=>Jt.fileIdToString(M.fileId),(M,G)=>{var L=ot(),y=Se(L);const _=xe(()=>a()+1);wa(y,{get data(){return n(G)},get wsContextModel(){return s()},get indentLevel(){return n(_)}}),l(M,L)}),l(E,x)};V(C,E=>{n(p)&&E(j)}),ge(E=>{we=Mt(N,1,"tree-item svelte-sympus",null,we,E),es(N,"title",(w(t()),f(()=>t().reason))),es(N,"aria-expanded",(w(t()),w(It),f(()=>t().type===It.folder&&t().expanded))),es(N,"aria-level",a()),Ra(N,`padding-left: ${10*a()+20}px;`),Ze(O,(w(t()),f(()=>t().name))),es(A,"src",(w(t()),f(()=>i[t().inclusionState]))),es(A,"alt",(w(t()),f(()=>d[t().inclusionState])))},[()=>({"included-folder":n(c)})],xe),wt("click",N,o),wt("keyup",N,function(...E){var x;(x=n(he))==null||x.apply(this,E)}),l(r,pe),rt()}var Gi=g('<div class="files-container svelte-8hfqhl"></div>'),Ji=$t('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Wi(r){var e=Ji();l(r,e)}var Ki=g('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Yi=g("<!> <!>",1),Xi=g('<div class="settings-card-body"><!></div>'),Qi=g('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function zt(r,e){const t=Ia(e),s=rs(e,["children","$$slots","$$events","$$legacy"]),a=rs(s,["class","icon","title","isClickable"]);st(e,!1);const o=F(),i=F(),d=F();let c=b(e,"class",8,""),u=b(e,"icon",24,()=>{}),p=b(e,"title",24,()=>{}),pe=b(e,"isClickable",8,!1);me(()=>(n(o),n(i),w(a)),()=>{m(o,a.class),m(i,Ea(a,["class"]))}),me(()=>(w(c()),n(o)),()=>{m(d,`settings-card ${c()} ${n(o)||""}`)}),ht();var N=Qi();_s(N,j=>({role:"button",class:n(d),...n(i),[La]:j}),[()=>({clickable:pe()})],"svelte-13uht7n");var he=h(N),we=h(he),D=h(we),I=j=>{var E=Yi(),x=Se(E),M=y=>{var _=Ki(),ie=h(_);Sr(ie,u,(z,k)=>{k(z,{})}),l(y,_)};V(x,y=>{u()&&y(M)});var G=v(x,2),L=y=>{de(y,{color:"neutral",size:1,weight:"light",class:"card-title",children:(_,ie)=>{var z=R();ge(()=>Ze(z,p())),l(_,z)},$$slots:{default:!0}})};V(G,y=>{p()&&y(L)}),l(j,E)},O=j=>{var E=ot(),x=Se(E);xt(x,e,"header-left",{},null),l(j,E)};V(D,j=>{u()||p()?j(I):j(O,!1)});var re=v(we,2),K=h(re);xt(K,e,"header-right",{},null);var A=v(he,2),C=j=>{var E=Xi(),x=h(E);xt(x,e,"default",{},null),l(j,E)};V(A,j=>{f(()=>t.default)&&j(C)}),wt("click",N,function(j){Fa.call(this,e,j)}),l(r,N),rt()}var eo=g('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),to=g('<div slot="header-right"><!></div>');function so(r,e){st(e,!1);const[t,s]=Et(),a=()=>nt(i,"$wsContextModel",t),o=F();let i=new Jt(ct,new pn(ct.postMessage)),d=F(),c=F();me(()=>a(),()=>{m(d,a().sourceFolders.sort((u,p)=>u.isWorkspaceFolder!==p.isWorkspaceFolder?u.isWorkspaceFolder?-1:1:u.fileId.folderRoot.localeCompare(p.fileId.folderRoot)))}),me(()=>a(),()=>{m(c,a().syncStatus)}),me(()=>n(d),()=>{m(o,n(d).reduce((u,p)=>u+(p.trackedFileCount??0),0))}),ht(),at(),wt("message",Rr,function(...u){var p;(p=i.handleMessageFromExtension)==null||p.apply(this,u)}),zt(r,{get icon(){return Wi},title:"Context",$$events:{contextmenu:u=>u.preventDefault()},children:(u,p)=>{var pe=eo(),N=h(pe),he=h(N);de(he,{size:1,weight:"medium",class:"context-section-header",children:(re,K)=>{var A=R("SOURCE FOLDERS");l(re,A)},$$slots:{default:!0}}),function(re,K){st(K,!1);let A=b(K,"folders",24,()=>[]),C=b(K,"onAddMore",8),j=b(K,"onRemove",8);at();var E=Di(),x=h(E);ut(x,1,A,y=>Jt.fileIdToString(y.fileId),(y,_)=>{var ie=ji();let z;var k=h(ie),J=$e=>{var De=hs(()=>Xs("Enter",()=>j()(n(_).fileId.folderRoot)));as($e,{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$events:{click:()=>j()(n(_).fileId.folderRoot),keyup(...se){var Y;(Y=n(De))==null||Y.apply(this,se)}},children:(se,Y)=>{un(se)},$$slots:{default:!0}})};V(k,$e=>{n(_),f(()=>!n(_).isWorkspaceFolder)&&$e(J)});var U=v(k,2);const te=xe(()=>(n(_),f(()=>($e=>$e.isWorkspaceFolder?"root-folder":"folder")(n(_)))));da(U,{class:"source-folder-v-adjust",get icon(){return n(te)}});var ce=v(U,2),Ie=h(ce),Me=v(Ie),Z=h(Me),ue=v(ce,2),Ae=$e=>{de($e,{size:1,class:"file-count",children:(De,se)=>{var Y=R();ge(ze=>Ze(Y,ze),[()=>(n(_),f(()=>n(_).trackedFileCount.toLocaleString()))],xe),l(De,Y)},$$slots:{default:!0}})};V(ue,$e=>{n(_),f(()=>n(_).trackedFileCount)&&$e(Ae)}),ge($e=>{z=Mt(ie,1,"item svelte-1skknri",null,z,$e),Ze(Ie,`${n(_),f(()=>n(_).name)??""} `),Ze(Z,(n(_),f(()=>n(_).isPending?"(pending)":n(_).fileId.folderRoot)))},[()=>({"workspace-folder":n(_).isWorkspaceFolder})],xe),l(y,ie)});var M=v(x,2),G=hs(()=>Xs("Enter",C())),L=h(M);ms(L,{}),wt("keyup",M,function(...y){var _;(_=n(G))==null||_.apply(this,y)}),wt("click",M,function(...y){var _;(_=C())==null||_.apply(this,y)}),l(re,E),rt()}(v(he,2),{get folders(){return n(d)},onRemove:re=>i.removeSourceFolder(re),onAddMore:()=>i.addMoreSourceFolders()});var we=v(N,2),D=h(we),I=h(D);de(I,{size:1,weight:"medium",class:"context-section-header",children:(re,K)=>{var A=R("FILES");l(re,A)},$$slots:{default:!0}});var O=v(I,2);de(O,{size:1,class:"file-count",children:(re,K)=>{var A=R();ge(C=>Ze(A,C),[()=>(n(o),f(()=>n(o).toLocaleString()))],xe),l(re,A)},$$slots:{default:!0}}),function(re,K){st(K,!1);const[A,C]=Et(),j=()=>nt(E(),"$wsContextModel",A);let E=b(K,"wsContextModel",8),x=F();me(()=>j(),()=>{m(x,j().sourceTree)}),ht(),at();var M=Gi();ut(M,5,()=>n(x),G=>Jt.fileIdToString(G.fileId),(G,L)=>{wa(G,{get wsContextModel(){return E()},get data(){return n(L)},indentLevel:0})}),l(re,M),rt(),C()}(v(D,2),{get wsContextModel(){return i}}),l(u,pe)},$$slots:{default:!0,"header-right":(u,p)=>{var pe=to(),N=h(pe),he=we=>{var D=hs(()=>Xs("Enter",()=>i.requestRefresh()));as(we,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>i.requestRefresh(),keyup(...I){var O;(O=n(D))==null||O.apply(this,I)}},children:(I,O)=>{Ka(I)},$$slots:{default:!0}})};V(N,we=>{n(c),w(wr),f(()=>n(c)===wr.done)&&we(he)}),l(u,pe)}}}),rt(),s()}function Tr(r){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function Yr(r){return Tr(r)&&"component"in r}var ro=$t('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function Xr(r){var e=ro();l(r,e)}var ao=g('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),no=g('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),io=g('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),oo=g('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function Qr(r,e){st(e,!1);let t=b(e,"item",8);at();var s=oo(),a=h(s);xt(a,e,"header",{},null);var o=v(a,2),i=h(o),d=c=>{var u=io(),p=Se(u);de(p,{size:4,weight:"medium",color:"neutral",children:(D,I)=>{var O=ao(),re=h(O);ge(()=>Ze(re,(w(t()),f(()=>{var K;return(K=t())==null?void 0:K.name})))),l(D,O)},$$slots:{default:!0}});var pe=v(p,2),N=D=>{de(D,{color:"secondary",size:1,weight:"light",children:(I,O)=>{var re=no(),K=h(re);ge(()=>Ze(K,(w(t()),f(()=>{var A;return(A=t())==null?void 0:A.description})))),l(I,re)},$$slots:{default:!0}})};V(pe,D=>{w(t()),f(()=>{var I;return(I=t())==null?void 0:I.description})&&D(N)});var he=v(pe,2),we=h(he);xt(we,e,"content",{get item(){return t()}},null),l(c,u)};V(i,c=>{t()!=null&&c(d)}),ge(()=>es(s,"id",(w(t()),f(()=>{var c;return(c=t())==null?void 0:c.id})))),l(r,s),rt()}function ks(r,e,t,s,a,o){return a!==void 0?{name:r,description:e,icon:t,id:s,component:a,props:o}:{name:r,description:e,icon:t,id:s}}var lo=g('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),co=g('<span class="c-navigation__head-icon"><!></span> ',1),uo=g("<button><!></button>"),ho=g('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),po=g('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),vo=g('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),mo=g("<span><!></span>"),go=g('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),fo=g('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),yo=g("<div><!></div>");function _o(r,e){st(e,!1);let t=b(e,"group",8,"Workspace Settings"),s=b(e,"items",24,()=>[]),a=b(e,"item",28,()=>{}),o=b(e,"mode",8,"tree"),i=b(e,"selectedId",28,()=>{}),d=b(e,"onNavigationChangeItem",8,D=>{}),c=b(e,"showButton",8,!0),u=b(e,"class",8,""),p=F(new Map);me(()=>(w(i()),w(a()),w(s())),()=>{var D;i()?a(s().find(I=>(I==null?void 0:I.id)===i())):i((D=a())==null?void 0:D.id)}),me(()=>(w(s()),w(t())),()=>{m(p,s().reduce((D,I)=>{if(!I)return D;const O=I.group??t(),re=D.get(O)??[];return re.push(I),D.set(O,re),D},new Map))}),me(()=>(w(a()),w(s())),()=>{a()||a(s()[0])}),me(()=>(w(d()),w(i())),()=>{d()(i())}),ht(),at();var pe=yo(),N=h(pe),he=D=>{gn(D,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return c()},minimized:!1,$$slots:{left:(I,O)=>{var re=po(),K=h(re);vn(K,i,A=>{var C=ot(),j=Se(C);ut(j,1,()=>n(p),Gt,(E,x)=>{var M=hs(()=>Or(n(x),2));let G=()=>n(M)[0];var L=ho(),y=h(L);xt(y,e,"group",{get label(){return G()},get mode(){return o()}},ie=>{var z=lo(),k=h(z);Xr(k);var J=v(k,2);de(J,{size:2,color:"primary",children:(U,te)=>{var ce=R();ge(()=>Ze(ce,G())),l(U,ce)},$$slots:{default:!0}}),l(ie,z)});var _=v(y,2);ut(_,5,()=>n(M)[1],Gt,(ie,z)=>{var k=uo();let J;var U=h(k);de(U,{size:2,weight:"regular",color:"primary",children:(te,ce)=>{var Ie=co(),Me=Se(Ie),Z=h(Me);Sr(Z,()=>n(z).icon,(Ae,$e)=>{$e(Ae,{})});var ue=v(Me);ge(()=>Ze(ue,` ${n(z),f(()=>n(z).name)??""}`)),l(te,Ie)},$$slots:{default:!0}}),ge(te=>J=Mt(k,1,"c-navigation__item svelte-n5ccbo",null,J,te),[()=>({"is-active":n(z).id===i()})],xe),wt("click",k,()=>{return te=n(z),a(te),void i(te==null?void 0:te.id);var te}),l(ie,k)}),l(E,L)}),l(A,C)}),l(I,re)},right:(I,O)=>{Qr(I,{get item(){return a()},slot:"right",$$slots:{header:(re,K)=>{var A=ot(),C=Se(A);xt(C,e,"header",{get item(){return a()},get selectedId(){return i()}},null),l(re,A)},content:(re,K)=>{var A=ot(),C=Se(A);xt(C,e,"content",{get item(){return a()},get isSelected(){return w(a()),w(i()),f(()=>{var j;return((j=a())==null?void 0:j.id)===i()})}},j=>{var E=ot(),x=Se(E),M=G=>{var L=ot(),y=Se(L);Sr(y,()=>a().component,(_,ie)=>{ie(_,za(()=>a().props))}),l(G,L)};V(x,G=>{w(Yr),w(a()),w(o()),w(i()),f(()=>{return Yr(a())&&(L=a(),y=o(),_=i(),y!=="tree"||(L==null?void 0:L.id)===_);var L,y,_})&&G(M)}),l(j,E)}),l(re,A)}}})}}})},we=D=>{var I=fo(),O=h(I);xt(O,e,"header",{get item(){return a()}},null);var re=v(O,2);ut(re,1,()=>n(p),Gt,(K,A)=>{var C=hs(()=>Or(n(A),2));let j=()=>n(C)[0];var E=go(),x=Se(E),M=h(x);xt(M,e,"group",{get label(){return j()},get mode(){return o()}},L=>{de(L,{color:"secondary",size:2,weight:"medium",children:(y,_)=>{var ie=vo(),z=Se(ie);Xr(h(z));var k=v(z,2),J=h(k);ge(()=>Ze(J,j())),l(y,ie)},$$slots:{default:!0}})});var G=v(x,2);ut(G,1,()=>n(C)[1],Gt,(L,y)=>{var _=mo();Qr(h(_),{get item(){return n(y)},$$slots:{content:(ie,z)=>{var k=ot(),J=Se(k);xt(J,e,"content",{get item(){return n(y)}},null),l(ie,k)}}}),ja(_,(ie,z)=>function(k,J){let U;function te({scrollTo:ce,delay:Ie,options:Me}){clearTimeout(U),ce&&(U=setTimeout(()=>{k.scrollIntoView(Me)},Ie))}return te(J),{update:te,destroy(){clearTimeout(U)}}}(ie,z),()=>({scrollTo:o()==="flat"&&n(y).id===i(),delay:300,options:{behavior:"smooth"}})),l(L,_)}),l(K,E)}),l(D,I)};V(N,D=>{o()==="tree"?D(he):D(we,!1)}),ge(()=>Mt(pe,1,`c-navigation c-navigation--mode__${o()??""} ${u()??""}`,"svelte-n5ccbo")),l(r,pe),rt()}var wo=$t('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function Co(r){var e=wo();l(r,e)}var bo=$t('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function $o(r){var e=bo();l(r,e)}var So=$t("<svg><!></svg>");function ea(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=So();_s(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),l(r,s)}var ko=g('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),xo=g("<span>Connect</span>"),Mo=g('<div class="connect-button-content svelte-js5lik"><!></div>'),Ao=g('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),To=g('<div slot="header-right"><!></div>'),No=g("<div> </div>"),Ro=g('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),Io=g('<div class="loading-container svelte-2bsejd"><!> <!></div>'),Eo=g('<div class="category-content"><!></div>'),Lo=g('<div class="category"><div class="category-heading"><!></div> <!></div>');const Ca="extensionClient";function ba(){const r=ps(Ca);if(!r)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return r}const $a="mcpServerModel";function Nr(){const r=ps($a);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}var zo=g('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),Zo=g("<span>Connect</span>"),Oo=g('<div class="connect-button-content svelte-e3a21z"><!></div>'),Po=g('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),Fo=g('<div slot="header-right"><!></div>'),jo=g("<div> </div>"),Do=g('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),Uo=g('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),Vo=g("<div><!></div>");function qo(r,e){st(e,!1);const[t,s]=Et(),a=()=>nt(D,"$allServers",t),o=()=>nt(we,"$pretendNativeToolDefs",t),i=F();let d=b(e,"title",8),c=b(e,"tools",24,()=>[]),u=b(e,"onAuthenticate",8),p=b(e,"onRevokeAccess",8),pe=b(e,"onToolApprovalConfigChange",8,()=>{});const N=ps(Rs.key),he=Nr(),we=N.getPretendNativeToolDefs(),D=he.getServers();me(()=>a(),()=>{m(i,N.getEnableNativeRemoteMcp()?aa(a()):[])}),ht(),at();var I=Vo(),O=h(I);const re=xe(()=>(w(c()),f(()=>c().length===0)));(function(K,A){let C=b(A,"title",8),j=b(A,"loading",8,!1);var E=Lo(),x=h(E),M=h(x);de(M,{size:1,color:"secondary",weight:"regular",children:(_,ie)=>{var z=R();ge(()=>Ze(z,C())),l(_,z)},$$slots:{default:!0}});var G=v(x,2),L=_=>{var ie=Io(),z=h(ie);Ys(z,{size:1});var k=v(z,2);de(k,{size:1,color:"secondary",children:(J,U)=>{var te=R("Loading...");l(J,te)},$$slots:{default:!0}}),l(_,ie)},y=_=>{var ie=Eo(),z=h(ie);xt(z,A,"default",{},null),l(_,ie)};V(G,_=>{j()?_(L):_(y,!1)}),l(K,E)})(O,{get title(){return d()},get loading(){return n(re)},children:(K,A)=>{var C=Uo(),j=h(C);ut(j,1,c,x=>x.name,(x,M)=>{(function(G,L){st(L,!1);let y=b(L,"config",8),_=b(L,"onAuthenticate",8),ie=b(L,"onRevokeAccess",8);const z=()=>{};let k=F(!1),J=F(null),U=F(!1);function te(){if(n(k))m(k,!1),n(J)&&(clearTimeout(n(J)),m(J,null));else{m(k,!0);const ue=y().authUrl||"";_()(ue),m(J,setTimeout(()=>{m(k,!1),m(J,null)},6e4))}}me(()=>(w(y()),n(k),n(J)),()=>{y().isConfigured&&n(k)&&(m(k,!1),n(J)&&(clearTimeout(n(J)),m(J,null)))}),ht(),at();var ce=Ro(),Ie=h(ce);zt(Ie,{get icon(){return w(y()),f(()=>y().icon)},get title(){return w(y()),f(()=>y().displayName)},$$slots:{"header-right":(ue,Ae)=>{var $e=To(),De=h($e),se=ze=>{const Te=xe(()=>n(k)?"neutral":"accent");Xe(ze,{variant:"ghost-block",get color(){return n(Te)},size:1,$$events:{click:te},children:(Ee,_e)=>{var Ce=Mo(),Oe=h(Ce),$=T=>{var oe=ko(),X=Se(oe),Q=h(X);Ys(Q,{size:1,useCurrentColor:!0}),l(T,oe)},q=T=>{var oe=xo();l(T,oe)};V(Oe,T=>{n(k)?T($):T(q,!1)}),l(Ee,Ce)},$$slots:{default:!0}})},Y=(ze,Te)=>{var Ee=_e=>{var Ce=Ao(),Oe=h(Ce),$=h(Oe),q=h($);let T;var oe=h(q);const X=xe(()=>(w(us),f(()=>[us.Hover])));Dt(oe,{get triggerOn(){return n(X)},content:"Revoke Access",children:(W,ae)=>{as(W,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>ie()(y())},children:(H,le)=>{ea(H,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var Q=v($,2);Cr.Root(Q,{color:"success",size:1,variant:"soft",children:(W,ae)=>{var H=R("Connected");l(W,H)},$$slots:{default:!0}}),ge(W=>T=Mt(q,1,"icon-button-wrapper svelte-js5lik",null,T,W),[()=>({active:n(U)})],xe),l(_e,Ce)};V(ze,_e=>{w(y()),f(()=>y().isConfigured)&&_e(Ee)},Te)};V(De,ze=>{w(y()),f(()=>!y().isConfigured&&y().authUrl)?ze(se):ze(Y,!1)}),l(ue,$e)}}});var Me=v(Ie,2),Z=ue=>{var Ae=No(),$e=h(Ae);ge(()=>{Mt(Ae,1,`status-message ${w(y()),f(()=>y().statusType)??""}`,"svelte-js5lik"),Ze($e,(w(y()),f(()=>y().statusMessage)))}),l(ue,Ae)};V(Me,ue=>{w(y()),f(()=>y().showStatus)&&ue(Z)}),wt("mouseenter",ce,()=>m(U,!0)),wt("mouseleave",ce,()=>m(U,!1)),l(G,ce),ua(L,"onToolApprovalConfigChange",z),rt({onToolApprovalConfigChange:z})})(x,{get config(){return n(M)},get onAuthenticate(){return u()},get onRevokeAccess(){return p()},onToolApprovalConfigChange:pe()})});var E=v(j,2);ut(E,1,o,x=>x.name,(x,M)=>{const G=xe(()=>(n(i),n(M),f(()=>n(i).find(L=>L.name===n(M).name))));(function(L,y){st(y,!1);let _=b(y,"config",12),ie=b(y,"mcpTool",8);const z=Nr(),k=ba();async function J(){if(n(te))return Ie&&(clearTimeout(Ie),Ie=null),void m(te,!1);k.startRemoteMCPAuth(_().name),m(te,!0);const $e=new Promise(De=>{Ie=setTimeout(()=>{De(),Ie=null},6e4)});await Promise.race([$e]),m(te,!1)}async function U(){await k.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${_().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(ie()&&z.deleteServer(ie()),m(te,!1))}let te=F(!1),ce=F(!1),Ie=null;me(()=>(w(_()),Ur),()=>{_(Ur(_()))}),me(()=>w(ie()),()=>{_(_().isConfigured=!!ie(),!0)}),ht(),at();var Me=Do(),Z=h(Me);zt(Z,{get icon(){return w(_()),f(()=>_().icon)},get title(){return w(_()),f(()=>_().displayName)},$$slots:{"header-right":($e,De)=>{var se=Fo(),Y=h(se),ze=Ee=>{const _e=xe(()=>n(te)?"neutral":"accent");Xe(Ee,{variant:"ghost-block",get color(){return n(_e)},size:1,$$events:{click:J},children:(Ce,Oe)=>{var $=Oo(),q=h($),T=X=>{var Q=zo(),W=Se(Q),ae=h(W);Ys(ae,{size:1,useCurrentColor:!0}),l(X,Q)},oe=X=>{var Q=Zo();l(X,Q)};V(q,X=>{n(te)?X(T):X(oe,!1)}),l(Ce,$)},$$slots:{default:!0}})},Te=(Ee,_e)=>{var Ce=Oe=>{var $=Po(),q=h($);let T;var oe=h(q);const X=xe(()=>(w(us),f(()=>[us.Hover])));Dt(oe,{get triggerOn(){return n(X)},content:"Revoke Access",children:(W,ae)=>{as(W,{color:"neutral",variant:"ghost",size:1,$$events:{click:U},children:(H,le)=>{ea(H,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var Q=v(q,2);Cr.Root(Q,{color:"success",size:1,variant:"soft",children:(W,ae)=>{var H=R("Connected");l(W,H)},$$slots:{default:!0}}),ge(W=>T=Mt(q,1,"disconnect-button svelte-e3a21z",null,T,W),[()=>({active:n(ce)})],xe),l(Oe,$)};V(Ee,Oe=>{w(_()),f(()=>_().isConfigured)&&Oe(Ce)},_e)};V(Y,Ee=>{w(_()),f(()=>!_().isConfigured)?Ee(ze):Ee(Te,!1)}),l($e,se)}}});var ue=v(Z,2),Ae=$e=>{var De=jo(),se=h(De);ge(()=>{Mt(De,1,`status-message ${w(_()),f(()=>_().statusType)??""}`,"svelte-e3a21z"),Ze(se,(w(_()),f(()=>_().statusMessage)))}),l($e,De)};V(ue,$e=>{w(_()),f(()=>_().showStatus)&&$e(Ae)}),wt("mouseenter",Me,()=>m(ce,!0)),wt("mouseleave",Me,()=>m(ce,!1)),l(L,Me),rt()})(x,{get mcpTool(){return n(G)},get config(){return n(M)}})}),l(K,C)},$$slots:{default:!0}}),l(r,I),rt(),s()}var Ho=g('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),Bo=g('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function Go(r,e){st(e,!1);let t=b(e,"handleEnterEditMode",8),s=b(e,"envVarEntries",28,()=>[]);at();var a=Bo(),o=Se(a);de(o,{size:1,weight:"medium",children:(N,he)=>{var we=R("Environment Variables");l(N,we)},$$slots:{default:!0}});var i=v(o,2),d=h(i),c=h(d),u=N=>{var he=ot(),we=Se(he);ut(we,1,s,D=>D.id,(D,I,O)=>{var re=Ho(),K=h(re),A=h(K);ts(A,{size:1,placeholder:"Name",class:"full-width",get value(){return n(I).key},set value(M){n(I).key=M,Pr(()=>s())},$$events:{focus(...M){var G;(G=t())==null||G.apply(this,M)},change:()=>function(M,G){const L=s().findIndex(y=>y.id===M);L!==-1&&(s(s()[L].key=G,!0),s(s()))}(n(I).id,n(I).key)},$$legacy:!0});var C=v(K),j=h(C);ts(j,{size:1,placeholder:"Value",class:"full-width",get value(){return n(I).value},set value(M){n(I).value=M,Pr(()=>s())},$$events:{focus(...M){var G;(G=t())==null||G.apply(this,M)},change:()=>function(M,G){const L=s().findIndex(y=>y.id===M);L!==-1&&(s(s()[L].value=G,!0),s(s()))}(n(I).id,n(I).value)},$$legacy:!0});var E=v(C),x=h(E);Dt(x,{content:"Remove",children:(M,G)=>{Xe(M,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...L){var y;(y=t())==null||y.apply(this,L)},click:()=>{return L=n(I).id,t()(),void s(s().filter(y=>y.id!==L));var L}},$$slots:{iconLeft:(L,y)=>{na(L,{slot:"iconLeft"})}}})},$$slots:{default:!0}}),l(D,re)}),l(N,he)};V(c,N=>{w(s()),f(()=>s().length>0)&&N(u)});var p=v(i,2),pe=h(p);Xe(pe,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){t()(),s([...s(),{id:crypto.randomUUID(),key:"",value:""}])}},children:(N,he)=>{var we=R("Variable");l(N,we)},$$slots:{default:!0,iconLeft:(N,he)=>{ms(N,{slot:"iconLeft"})}}}),l(r,a),rt()}var Jo=g("<div></div>"),Wo=g(" <!>",1),Ko=g('<div class="server-name svelte-1koxb3z"><!></div>'),Yo=g('<div slot="header-left" class="l-header svelte-1koxb3z"><!> <!> <!> <div class="command-text svelte-1koxb3z"><!></div></div>'),Xo=g('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),Qo=g('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),el=g('<div class="status-controls-button svelte-1koxb3z"><!> <!></div>'),tl=g("<!> <!> <!>",1),sl=g("<!> <!>",1),rl=g('<div class="server-actions svelte-1koxb3z" slot="header-right"><!> <div class="status-controls svelte-1koxb3z"><!> <!></div></div>'),al=g('<div class="c-tool-item svelte-1koxb3z"><div class="c-tool-info svelte-1koxb3z"><div class="tool-status svelte-1koxb3z"><div></div> <!></div> <div class="c-tool-description svelte-1koxb3z"><!></div></div></div>'),nl=g('<div slot="footer"></div>'),il=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>',1),ol=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!> <div class="connection-type-buttons svelte-1koxb3z"><!> <!></div></div></div>'),ll=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),dl=g('<div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div>'),cl=g('<!> <div class="form-row svelte-1koxb3z"><div class="input-field svelte-1koxb3z"><!></div></div> <!>',1),ul=g('<form><div class="server-edit-form svelte-1koxb3z"><div class="server-header svelte-1koxb3z"><div class="server-title svelte-1koxb3z"><div class="server-icon svelte-1koxb3z"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-1koxb3z"><div><!></div> <div class="form-actions svelte-1koxb3z"><!> <!></div></div></div></form>');function ta(r,e){var De;st(e,!1);const t=F(),s=F(),a=F(),o=F(),i=F(),d=F(),c=F(),u=F();let p=b(e,"server",8,null),pe=b(e,"onDelete",8),N=b(e,"onAdd",8),he=b(e,"onSave",8),we=b(e,"onEdit",8),D=b(e,"onToggleDisableServer",8),I=b(e,"onJSONImport",8),O=b(e,"onCancel",8),re=b(e,"onAuthenticate",24,()=>{}),K=b(e,"disabledText",24,()=>{}),A=b(e,"warningText",24,()=>{}),C=b(e,"mode",12,"view"),j=b(e,"mcpServerError",12,""),E=F(0),x=F(((De=p())==null?void 0:De.name)??""),M=F(kt(p())?"":cs(p())?p().command:""),G=F(kt(p())?p().url:""),L=cs(p())?p().env??{}:{},y=F(""),_=F(kt(p())?p().type:"http"),ie=F([]);k();let z=F(!0);function k(){m(ie,Object.entries(L).map(([se,Y])=>({id:crypto.randomUUID(),key:se,value:Y})))}let J=F(()=>{});function U(){p()&&C()==="view"&&(C("edit"),we()(p()),n(J)())}let te=b(e,"busy",12,!1);function ce({key:se,value:Y}){return se.trim()&&Y.trim()}async function Ie(){j(""),te(!0);const se=n(ie).filter(ce);L=Object.fromEntries(se.map(({key:Y,value:ze})=>[Y.trim(),ze.trim()])),k();try{if(C()==="add"){const Y={type:"stdio",name:n(x).trim(),command:n(M).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(L).length>0?L:void 0};await N()(Y)}else if(C()==="addRemote"){const Y={type:n(_),name:n(x).trim(),url:n(G).trim()};await N()(Y)}else if(C()==="addJson"){try{JSON.parse(n(y))}catch(Y){const ze=Y instanceof Error?Y.message:String(Y);throw new gt(`Invalid JSON format: ${ze}`)}await I()(n(y))}else if(C()==="edit"&&p()){if(kt(p())){const Y={...p(),type:n(_),name:n(x).trim(),url:n(G).trim()};await he()(Y)}else if(cs(p())){const Y={...p(),name:n(x).trim(),command:n(M).trim(),arguments:"",env:Object.keys(L).length>0?L:void 0};await he()(Y)}}}catch(Y){j(Y instanceof gt?Y.message:"Failed to save server"),console.warn(Y)}finally{te(!1)}}function Me(){var se,Y;te(!1),j(""),(se=O())==null||se(),m(y,""),m(x,((Y=p())==null?void 0:Y.name)??""),m(M,kt(p())?"":cs(p())?p().command:""),m(G,kt(p())?p().url:""),L=cs(p())&&p().env?{...p().env}:{},m(_,kt(p())?p().type:"http"),k()}me(()=>w(p()),()=>{var se;m(t,((se=p())==null?void 0:se.tools)??[])}),me(()=>w(p()),()=>{m(s,kt(p()))}),me(()=>n(E),()=>{m(a,Date.now()-n(E)<3e3)}),me(()=>(w(p()),n(s),n(t),n(a)),()=>{var se;m(o,!((se=p())!=null&&se.disabled)&&n(s)&&p().authRequired===!0&&n(t).length===0&&!n(a))}),me(()=>(n(x),n(M)),()=>{n(x)&&n(M)&&j("")}),me(()=>(w(C()),n(x),n(M),n(G)),()=>{m(i,!((C()!=="add"||n(x).trim()&&n(M).trim())&&(C()!=="addRemote"||n(x).trim()&&n(G).trim())))}),me(()=>(w(C()),n(y)),()=>{m(d,C()==="addJson"&&!n(y).trim())}),me(()=>(n(i),w(C()),n(d)),()=>{m(c,n(i)||C()==="view"||n(d))}),me(()=>w(C()),()=>{m(u,(()=>{switch(C()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),ht(),at();var Z=ot();ge(()=>{console.log({server:f(()=>qr(p())),serverTools:f(()=>qr(n(t)))})});var ue=Se(Z),Ae=se=>{ca(se,{get collapsed(){return n(z)},set collapsed(Y){m(z,Y)},$$slots:{header:(Y,ze)=>{zt(Y,{slot:"header",$$slots:{"header-left":(Te,Ee)=>{var _e=Yo(),Ce=h(_e),Oe=W=>{as(W,{size:1,variant:"ghost",$$events:{click:()=>m(z,!n(z))},children:(ae,H)=>{var le=ot(),Ve=Se(le),ee=ke=>{nn(ke,{})},ve=ke=>{Er(ke,{})};V(Ve,ke=>{n(z)?ke(ee):ke(ve,!1)}),l(ae,le)},$$slots:{default:!0}})};V(Ce,W=>{n(t),f(()=>n(t).length>0)&&W(Oe)});var $=v(Ce,2);const q=xe(()=>K()||(n(o)?"Authentication required":A()));Dt($,{get content(){return n(q)},children:(W,ae)=>{var H=Jo();let le;ge(Ve=>le=Mt(H,1,"c-dot svelte-1koxb3z",null,le,Ve),[()=>({"c-green":!K()&&!n(o),"c-warning":n(o)||!K()&&!!A(),"c-red":!!K()&&!n(o),"c-disabled":p().disabled})],xe),l(W,H)},$$slots:{default:!0}});var T=v($,2);Dt(T,{get content(){return w(p()),f(()=>p().name)},side:"top",align:"start",children:(W,ae)=>{var H=Ko(),le=h(H);de(le,{size:1,weight:"medium",children:(Ve,ee)=>{var ve=Wo(),ke=Se(ve),Pe=v(ke),Ue=We=>{var Ne=R();ge(()=>Ze(Ne,`(${n(t),f(()=>n(t).length)??""}) tools`)),l(We,Ne)};V(Pe,We=>{n(t),f(()=>n(t).length>0)&&We(Ue)}),ge(()=>Ze(ke,`${w(p()),f(()=>p().name)??""} `)),l(Ve,ve)},$$slots:{default:!0}}),l(W,H)},$$slots:{default:!0}});var oe=v(T,2),X=h(oe);const Q=xe(()=>(w(Ks),w(p()),f(()=>Ks(p()))));Dt(X,{get content(){return n(Q)},side:"top",align:"start",children:(W,ae)=>{de(W,{color:"secondary",size:1,weight:"regular",children:(H,le)=>{var Ve=R();ge(ee=>Ze(Ve,ee),[()=>(w(Ks),w(p()),f(()=>Ks(p())))],xe),l(H,Ve)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(Te,_e)},"header-right":(Te,Ee)=>{var _e=rl(),Ce=h(_e),Oe=X=>{Xe(X,{size:1,variant:"surface",color:"warning",$$events:{click:()=>{var Q;(Q=re())==null||Q(p())}},children:(Q,W)=>{var ae=R("Authenticate");l(Q,ae)},$$slots:{default:!0}})};V(Ce,X=>{n(o)&&X(Oe)});var $=v(Ce,2),q=h($),T=X=>{const Q=xe(()=>(w(p()),f(()=>!p().disabled)));br(X,{size:1,get checked(){return n(Q)},$$events:{change:()=>{p()&&(m(E,Date.now()),D()(p().id)),n(J)()}}})};V(q,X=>{w(jr),f(jr)&&X(T)});var oe=v(q,2);tt.Root(oe,{get requestClose(){return n(J)},set requestClose(X){m(J,X)},children:(X,Q)=>{var W=sl(),ae=Se(W);tt.Trigger(ae,{children:(le,Ve)=>{as(le,{size:1,variant:"ghost-block",color:"neutral",children:(ee,ve)=>{yn(ee,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var H=v(ae,2);tt.Content(H,{side:"bottom",align:"end",children:(le,Ve)=>{var ee=tl(),ve=Se(ee);tt.Item(ve,{onSelect:U,children:(Ue,We)=>{var Ne=Xo(),be=h(Ne);_n(be,{});var Be=v(be,2);de(Be,{size:1,weight:"medium",children:(Qe,Ct)=>{var lt=R("Edit");l(Qe,lt)},$$slots:{default:!0}}),l(Ue,Ne)},$$slots:{default:!0}});var ke=v(ve,2);tt.Item(ke,{onSelect:()=>{(function(){if(p()){const Ue=Gs.convertServerToJSON(p());navigator.clipboard.writeText(Ue)}})(),n(J)()},children:(Ue,We)=>{var Ne=Qo(),be=h(Ne);Cn(be,{});var Be=v(be,2);de(Be,{size:1,weight:"medium",children:(Qe,Ct)=>{var lt=R("Copy JSON");l(Qe,lt)},$$slots:{default:!0}}),l(Ue,Ne)},$$slots:{default:!0}});var Pe=v(ke,2);tt.Item(Pe,{color:"error",onSelect:()=>{pe()(p()),n(J)()},children:(Ue,We)=>{var Ne=el(),be=h(Ne);na(be,{});var Be=v(be,2);de(Be,{size:1,weight:"medium",children:(Qe,Ct)=>{var lt=R("Delete");l(Qe,lt)},$$slots:{default:!0}}),l(Ue,Ne)},$$slots:{default:!0}}),l(le,ee)},$$slots:{default:!0}}),l(X,W)},$$slots:{default:!0},$$legacy:!0}),l(Te,_e)}}})},footer:(Y,ze)=>{var Te=nl();ut(Te,5,()=>n(t),Gt,(Ee,_e)=>{var Ce=al(),Oe=h(Ce),$=h(Oe),q=h($);let T;var oe=v(q,2);de(oe,{size:1,weight:"medium",children:(W,ae)=>{var H=R();ge(()=>Ze(H,(n(_e),f(()=>n(_e).definition.mcp_tool_name||n(_e).definition.name)))),l(W,H)},$$slots:{default:!0}});var X=v($,2),Q=h(X);Dt(Q,{get content(){return n(_e),f(()=>n(_e).definition.description)},align:"start",children:(W,ae)=>{var H=ot(),le=Se(H),Ve=ee=>{de(ee,{size:1,color:"secondary",children:(ve,ke)=>{var Pe=R();ge(()=>Ze(Pe,(n(_e),f(()=>n(_e).definition.description)))),l(ve,Pe)},$$slots:{default:!0}})};V(le,ee=>{n(_e),f(()=>n(_e).definition.description)&&ee(Ve)}),l(W,H)},$$slots:{default:!0}}),ge(W=>T=Mt(q,1,"tool-status-dot svelte-1koxb3z",null,T,W),[()=>({enabled:n(_e).enabled,disabled:!n(_e).enabled})],xe),l(Ee,Ce)}),l(Y,Te)}},$$legacy:!0})},$e=se=>{var Y=ul(),ze=h(Y),Te=h(ze),Ee=h(Te),_e=h(Ee),Ce=h(_e);Ya(Ce);var Oe=v(_e,2);de(Oe,{color:"secondary",size:1,weight:"medium",children:(ve,ke)=>{var Pe=R();ge(()=>Ze(Pe,n(u))),l(ve,Pe)},$$slots:{default:!0}});var $=v(Te,2),q=ve=>{var ke=il(),Pe=Se(ke),Ue=h(Pe),We=h(Ue);de(We,{size:1,weight:"medium",children:(Qe,Ct)=>{var lt=R("Code Snippet");l(Qe,lt)},$$slots:{default:!0}});var Ne=v(Pe,2),be=h(Ne),Be=h(be);ha(Be,{size:1,placeholder:"Paste JSON here...",get value(){return n(y)},set value(Qe){m(y,Qe)},$$legacy:!0}),l(ve,ke)},T=(ve,ke)=>{var Pe=Ue=>{var We=cl(),Ne=Se(We),be=dt=>{var Ke=ol(),it=h(Ke),At=h(it);de(At,{size:1,weight:"medium",children:(Cs,zr)=>{var bs=R("Connection Type");l(Cs,bs)},$$slots:{default:!0}});var pt=v(At,2),Fe=h(pt);const Ye=xe(()=>n(_)==="http"?"solid":"ghost"),yt=xe(()=>n(_)==="http"?"accent":"neutral");Xe(Fe,{size:1,get variant(){return n(Ye)},get color(){return n(yt)},type:"button",$$events:{click:()=>m(_,"http")},children:(Cs,zr)=>{var bs=R("HTTP");l(Cs,bs)},$$slots:{default:!0}});var Pt=v(Fe,2);const ur=xe(()=>n(_)==="sse"?"solid":"ghost"),ka=xe(()=>n(_)==="sse"?"accent":"neutral");Xe(Pt,{size:1,get variant(){return n(ur)},get color(){return n(ka)},type:"button",$$events:{click:()=>m(_,"sse")},children:(Cs,zr)=>{var bs=R("SSE");l(Cs,bs)},$$slots:{default:!0}}),l(dt,Ke)};V(Ne,dt=>{w(C()),w(p()),f(()=>{var Ke,it;return C()==="addRemote"||C()==="edit"&&(((Ke=p())==null?void 0:Ke.type)==="http"||((it=p())==null?void 0:it.type)==="sse")})&&dt(be)});var Be=v(Ne,2),Qe=h(Be),Ct=h(Qe);ts(Ct,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return n(x)},set value(dt){m(x,dt)},$$events:{focus:U},$$slots:{label:(dt,Ke)=>{de(dt,{slot:"label",size:1,weight:"medium",children:(it,At)=>{var pt=R("Name");l(it,pt)},$$slots:{default:!0}})}},$$legacy:!0});var lt=v(Be,2),qt=dt=>{var Ke=ll(),it=h(Ke),At=h(it);ts(At,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return n(G)},set value(pt){m(G,pt)},$$events:{focus:U},$$slots:{label:(pt,Fe)=>{de(pt,{slot:"label",size:1,weight:"medium",children:(Ye,yt)=>{var Pt=R("URL");l(Ye,Pt)},$$slots:{default:!0}})}},$$legacy:!0}),l(dt,Ke)},ws=dt=>{var Ke=dl(),it=h(Ke),At=h(it);ts(At,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return n(M)},set value(pt){m(M,pt)},$$events:{focus:U},$$slots:{label:(pt,Fe)=>{de(pt,{slot:"label",size:1,weight:"medium",children:(Ye,yt)=>{var Pt=R("Command");l(Ye,Pt)},$$slots:{default:!0}})}},$$legacy:!0}),l(dt,Ke)};V(lt,dt=>{w(C()),w(p()),f(()=>{var Ke,it;return C()==="addRemote"||((Ke=p())==null?void 0:Ke.type)==="http"||((it=p())==null?void 0:it.type)==="sse"})?dt(qt):dt(ws,!1)}),l(Ue,We)};V(ve,Ue=>{C()!=="add"&&C()!=="addRemote"&&C()!=="edit"||Ue(Pe)},ke)};V($,ve=>{C()==="addJson"?ve(q):ve(T,!1)});var oe=v($,2),X=ve=>{Go(ve,{handleEnterEditMode:U,get envVarEntries(){return n(ie)},set envVarEntries(ke){m(ie,ke)},$$legacy:!0})};V(oe,ve=>{w(C()),w(kt),w(p()),f(()=>(C()==="add"||C()==="edit")&&!kt(p()))&&ve(X)});var Q=v(oe,2),W=h(Q);let ae;var H=h(W);Ns(H,{variant:"soft",color:"error",size:1,children:(ve,ke)=>{var Pe=R();ge(()=>Ze(Pe,j())),l(ve,Pe)},$$slots:{default:!0,icon:(ve,ke)=>{Xa(ve,{slot:"icon"})}}});var le=v(W,2),Ve=h(le);Xe(Ve,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:Me},children:(ve,ke)=>{var Pe=R("Cancel");l(ve,Pe)},$$slots:{default:!0}});var ee=v(Ve,2);Xe(ee,{size:1,variant:"solid",color:"accent",get loading(){return te()},type:"submit",get disabled(){return n(c)},children:(ve,ke)=>{var Pe=ot(),Ue=Se(Pe),We=be=>{var Be=R("Import");l(be,Be)},Ne=(be,Be)=>{var Qe=lt=>{var qt=R("Add");l(lt,qt)},Ct=(lt,qt)=>{var ws=Ke=>{var it=R("Add");l(Ke,it)},dt=(Ke,it)=>{var At=pt=>{var Fe=R("Save");l(pt,Fe)};V(Ke,pt=>{C()==="edit"&&pt(At)},it)};V(lt,Ke=>{C()==="addRemote"?Ke(ws):Ke(dt,!1)},qt)};V(be,lt=>{C()==="add"?lt(Qe):lt(Ct,!1)},Be)};V(Ue,be=>{C()==="addJson"?be(We):be(Ne,!1)}),l(ve,Pe)},$$slots:{default:!0}}),ge(ve=>{Mt(Y,1,"c-mcp-server-card "+(C()==="add"||C()==="addJson"||C()==="addRemote"?"add-server-section":"server-item"),"svelte-1koxb3z"),ae=Mt(W,1,"error-container svelte-1koxb3z",null,ae,ve)},[()=>({"is-error":!!j()})],xe),wt("submit",Y,fn(Ie)),l(se,Y)};return V(ue,se=>{C()==="view"&&p()?se(Ae):se($e,!1)}),l(r,Z),ua(e,"setLocalEnvVarFormState",k),rt({setLocalEnvVarFormState:k})}var hl=g('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),pl=g('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),vl=g('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),ml=g('<div class="installed-indicator svelte-8tbe79"><!></div>'),gl=g('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),fl=g('<div class="mcp-service-item"><!></div>'),yl=g('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),_l=g('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),wl=g('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),Cl=g('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function bl(r,e){st(e,!1);let t=b(e,"onMCPServerAdd",24,()=>{}),s=b(e,"servers",24,()=>[]);const a=[{label:vr.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:vr.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:vr.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],o="easyMCPInstall.collapsed";let i=F(!1),d=F(!1),c=F(null),u=F({}),p=F({});function pe(D){var K;if(!D.userInput)return;for(let A=0;A<D.userInput.length;A++){const C=D.userInput[A];let j;if(j=C.type==="environmentVariable"&&C.envVarName?C.envVarName:C.correspondingArg?C.correspondingArg:`input_${A}`,!((K=n(u)[j])==null?void 0:K.trim())){const x=n(p)[j];return void(x&&x.focus())}}let I=[D.command],O={};D.args&&I.push(...D.args);for(let A=0;A<D.userInput.length;A++){const C=D.userInput[A];let j;j=C.type==="environmentVariable"&&C.envVarName?C.envVarName:C.correspondingArg?C.correspondingArg:`input_${A}`;const E=n(u)[j].trim(),x=`"${E}"`;if(C.type==="environmentVariable"&&C.envVarName)O[C.envVarName]=E;else if(C.correspondingArg){const M=I.indexOf(C.correspondingArg);M!==-1?I.splice(M+1,0,x):I.push(C.correspondingArg,x)}else I.push(x)}const re={type:"stdio",name:D.label,command:I.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(O).length>0?O:void 0};t()&&t()(re),m(c,null),m(u,{})}function N(){m(c,null),m(u,{})}me(()=>{},()=>{const D=localStorage.getItem(o);if(D!==null)try{m(i,JSON.parse(D))}catch{localStorage.removeItem(o)}m(d,!0)}),me(()=>(n(d),n(i)),()=>{typeof window<"u"&&n(d)&&localStorage.setItem(o,JSON.stringify(n(i)))}),ht(),at();var he=Cl(),we=h(he);ca(we,{get collapsed(){return n(i)},set collapsed(D){m(i,D)},children:(D,I)=>{var O=yl(),re=h(O);ut(re,5,()=>a,Gt,(K,A)=>{var C=fl();zt(h(C),{$$slots:{"header-left":(j,E)=>{var x=vl(),M=h(x),G=h(M);de(G,{size:1,weight:"medium",children:(z,k)=>{var J=R();ge(()=>Ze(J,(n(A),f(()=>n(A).label)))),l(z,J)},$$slots:{default:!0}});var L=v(M,2),y=z=>{de(z,{size:1,color:"secondary",children:(k,J)=>{var U=R();ge(()=>Ze(U,(n(A),f(()=>n(A).description)))),l(k,U)},$$slots:{default:!0}})};V(L,z=>{n(A),f(()=>n(A).description)&&z(y)});var _=v(L,2),ie=z=>{var k=pl(),J=h(k);ut(J,1,()=>(n(A),f(()=>n(A).userInput)),Gt,(Ie,Me,Z)=>{var ue=hl();const Ae=xe(()=>(n(Me),f(()=>n(Me).type==="environmentVariable"&&n(Me).envVarName?n(Me).envVarName:n(Me).correspondingArg||`input_${Z}`)));var $e=h(ue);de($e,{size:1,weight:"medium",color:"neutral",children:(Te,Ee)=>{var _e=R();ge(()=>Ze(_e,(n(Me),f(()=>n(Me).label)))),l(Te,_e)},$$slots:{default:!0}});var De=v($e,2),se=Te=>{de(Te,{size:1,color:"secondary",children:(Ee,_e)=>{var Ce=R();ge(()=>Ze(Ce,(n(Me),f(()=>n(Me).description)))),l(Ee,Ce)},$$slots:{default:!0}})};V(De,Te=>{n(Me),f(()=>n(Me).description)&&Te(se)});var Y=v(De,2);const ze=xe(()=>(n(Me),f(()=>n(Me).placeholder||"")));ts(Y,{get placeholder(){return n(ze)},size:1,variant:"surface",get value(){return n(u)[n(Ae)]},set value(Te){hr(u,n(u)[n(Ae)]=Te)},get textInput(){return n(p)[n(Ae)]},set textInput(Te){hr(p,n(p)[n(Ae)]=Te)},$$events:{keydown:Te=>{Te.key==="Enter"?pe(n(A)):Te.key==="Escape"&&N()}},$$legacy:!0}),l(Ie,ue)});var U=v(J,2),te=h(U);Xe(te,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>pe(n(A))},children:(Ie,Me)=>{var Z=R("Install");l(Ie,Z)},$$slots:{default:!0}});var ce=v(te,2);Xe(ce,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:N},children:(Ie,Me)=>{var Z=R("Cancel");l(Ie,Z)},$$slots:{default:!0}}),l(z,k)};V(_,z=>{n(c),n(A),f(()=>n(c)===n(A).label&&n(A).userInput)&&z(ie)}),l(j,x)},"header-right":(j,E)=>{var x=gl(),M=h(x),G=y=>{var _=ml(),ie=h(_);Cr.Root(ie,{color:"success",size:1,variant:"soft",children:(z,k)=>{var J=R("Installed");l(z,J)},$$slots:{default:!0}}),l(y,_)},L=(y,_)=>{var ie=z=>{as(z,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(k){if(s().some(U=>U.name===k.label))return;if(k.userInput&&k.userInput.length>0)return m(u,{}),k.userInput.forEach((U,te)=>{let ce;ce=U.type==="environmentVariable"&&U.envVarName?U.envVarName:U.correspondingArg?U.correspondingArg:`input_${te}`,hr(u,n(u)[ce]=U.defaultValue||"")}),void m(c,k.label);const J={type:"stdio",name:k.label,command:k.command,arguments:"",useShellInterpolation:!0};t()&&t()(J)}(n(A))},children:(k,J)=>{ms(k,{})},$$slots:{default:!0}})};V(y,z=>{n(c),n(A),f(()=>n(c)!==n(A).label)&&z(ie)},_)};V(M,y=>{w(s()),n(A),f(()=>s().some(_=>_.name===n(A).label))?y(G):y(L,!1)}),l(j,x)}}}),l(K,C)}),l(D,O)},$$slots:{default:!0,header:(D,I)=>{var O=wl();zt(h(O),{$$slots:{"header-left":(re,K)=>{var A=_l(),C=h(A);mn(C,{});var j=v(C,2);de(j,{color:"neutral",size:1,weight:"light",class:"card-title",children:(E,x)=>{var M=R("Easy MCP Installation");l(E,M)},$$slots:{default:!0}}),l(re,A)}}}),l(D,O)}},$$legacy:!0}),l(r,he),rt()}const $l={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},Sl={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},kl=Da(),xl=new class{constructor(r){je(this,"strings");let e={[pr.vscode]:{},[pr.jetbrains]:Sl,[pr.web]:{}};this.strings={...$l,...e[r]}}get(r){return this.strings[r]}}(kl.clientType);var Ml=g('<div class="section-heading-text">MCP</div>'),Al=g(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),Tl=g('<div class="section-heading-text">Terminal</div>'),Nl=g("<!> <!>",1),Rl=g('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function Il(r,e){st(e,!1);const t=F();let s=b(e,"supportedShells",24,()=>[]),a=b(e,"selectedShell",24,()=>{}),o=b(e,"startupScript",28,()=>{}),i=b(e,"onShellSelect",8),d=b(e,"onStartupScriptChange",8),c=F();me(()=>w(a()),()=>{var O;m(t,a()?(O=a(),s().find(re=>re.friendlyName===O)):void 0)}),ht(),at();var u=Rl(),p=h(u);de(p,{size:1,weight:"regular",color:"secondary",children:(O,re)=>{var K=Tl();l(O,K)},$$slots:{default:!0}});var pe=v(p,2),N=h(pe);de(N,{size:1,children:(O,re)=>{var K=R("Shell:");l(O,K)},$$slots:{default:!0}});var he=v(N,2);tt.Root(he,{get requestClose(){return n(c)},set requestClose(O){m(c,O)},children:(O,re)=>{var K=Nl(),A=Se(K);tt.Trigger(A,{children:(j,E)=>{const x=xe(()=>(w(s()),f(()=>s().length===0)));Xe(j,{size:1,variant:"outline",color:"neutral",get disabled(){return n(x)},children:(M,G)=>{var L=ot(),y=Se(L),_=z=>{var k=R();ge(()=>Ze(k,`${n(t),f(()=>n(t).friendlyName)??""}
            (${n(t),f(()=>n(t).supportString)??""})`)),l(z,k)},ie=(z,k)=>{var J=te=>{var ce=R("No shells available");l(te,ce)},U=te=>{var ce=R("Select a shell");l(te,ce)};V(z,te=>{w(s()),f(()=>s().length===0)?te(J):te(U,!1)},k)};V(y,z=>{n(t),w(s()),f(()=>n(t)&&s().length>0)?z(_):z(ie,!1)}),l(M,L)},$$slots:{default:!0,iconRight:(M,G)=>{en(M)}}})},$$slots:{default:!0}});var C=v(A,2);tt.Content(C,{side:"bottom",align:"start",children:(j,E)=>{var x=ot(),M=Se(x),G=y=>{var _=ot(),ie=Se(_);ut(ie,1,s,z=>z.friendlyName,(z,k)=>{const J=xe(()=>(w(a()),n(k),f(()=>a()===n(k).friendlyName)));tt.Item(z,{onSelect:()=>{i()(n(k).friendlyName),n(c)()},get highlight(){return n(J)},children:(U,te)=>{var ce=R();ge(()=>Ze(ce,`${n(k),f(()=>n(k).friendlyName)??""}
              (${n(k),f(()=>n(k).supportString)??""})`)),l(U,ce)},$$slots:{default:!0}})}),l(y,_)},L=y=>{tt.Label(y,{children:(_,ie)=>{var z=R("No shells available");l(_,z)},$$slots:{default:!0}})};V(M,y=>{w(s()),f(()=>s().length>0)?y(G):y(L,!1)}),l(j,x)},$$slots:{default:!0}}),l(O,K)},$$slots:{default:!0},$$legacy:!0});var we=v(pe,2),D=h(we);de(D,{size:1,children:(O,re)=>{var K=R("Start-up script: Code to run wherever a new terminal is opened");l(O,K)},$$slots:{default:!0}});var I=v(D,2);ha(I,{placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return o()},set value(O){o(O)},$$events:{change:function(O){const re=O.target;d()(re.value)}},$$legacy:!0}),l(r,u),rt()}var El=g('<div class="section-heading-text">Sound Settings</div>'),Ll=g('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),zl=g('<div slot="header-right"><!></div>'),Zl=g('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),Ol=g('<div slot="header-right"><!></div>'),Pl=g('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),Fl=g('<div class="section-heading-text">Agent Settings</div>'),jl=g('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),Dl=g('<div slot="header-right"><!></div>'),Ul=g('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),Vl=g('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function ql(r,e){let t=b(e,"tools",24,()=>[]),s=b(e,"isMCPEnabled",8,!0),a=b(e,"isMCPImportEnabled",8,!0),o=b(e,"isTerminalEnabled",8,!0),i=b(e,"isSoundCategoryEnabled",8,!1),d=b(e,"isAgentCategoryEnabled",8,!1),c=b(e,"isSwarmModeFeatureFlagEnabled",8,!1),u=b(e,"hasEverUsedRemoteAgent",8,!1),p=b(e,"onAuthenticate",8),pe=b(e,"onRevokeAccess",8),N=b(e,"onToolApprovalConfigChange",8,()=>{}),he=b(e,"onMCPServerAdd",8),we=b(e,"onMCPServerSave",8),D=b(e,"onMCPServerDelete",8),I=b(e,"onMCPServerToggleDisable",8),O=b(e,"onMCPServerJSONImport",8),re=b(e,"onCancel",24,()=>{}),K=b(e,"supportedShells",24,()=>[]),A=b(e,"selectedShell",24,()=>{}),C=b(e,"startupScript",24,()=>{}),j=b(e,"onShellSelect",8,()=>{}),E=b(e,"onStartupScriptChange",8,()=>{});var x=Vl(),M=h(x);qo(M,{title:"Services",get tools(){return t()},get onAuthenticate(){return p()},get onRevokeAccess(){return pe()},onToolApprovalConfigChange:N()});var G=v(M,2),L=U=>{(function(te,ce){st(ce,!1);const[Ie,Me]=Et(),Z=()=>nt(W,"$allServers",Ie),ue=F(),Ae=F();let $e=b(ce,"onMCPServerAdd",8),De=b(ce,"onMCPServerSave",8),se=b(ce,"onMCPServerDelete",8),Y=b(ce,"onMCPServerToggleDisable",8),ze=b(ce,"onCancel",24,()=>{}),Te=b(ce,"onMCPServerJSONImport",8),Ee=b(ce,"isMCPImportEnabled",8,!0);const _e=ba();function Ce(Fe){_e.startRemoteMCPAuth(Fe.name)}let Oe=F(null),$=F(null);function q(){var Fe;m(Oe,null),m($,null),(Fe=ze())==null||Fe()}let T=F([]);const oe=ps(Rs.key),X=Nr(),Q=oe.getEnableNativeRemoteMcp(),W=X.getServers();function ae(Fe){m(Oe,Fe.id)}function H(Fe){return async function(...Ye){const yt=await Fe(...Ye);return m($,null),m(Oe,null),yt}}const le=H($e()),Ve=H(De()),ee=H(Te()),ve=H(se()),ke=H(Y()),Pe=xl.get("mcpDocsURL");me(()=>(n($),n(Oe)),()=>{m(ue,n($)==="add"||n($)==="addJson"||n($)==="addRemote"||n(Oe)!==null)}),me(()=>Z(),()=>{m(T,Q?Qa(Z()):Z())}),me(()=>n(T),()=>{m(Ae,Gs.parseServerValidationMessages(n(T)))}),ht(),at();var Ue=Al(),We=Se(Ue),Ne=h(We),be=h(Ne);de(be,{size:1,weight:"regular",color:"secondary",children:(Fe,Ye)=>{var yt=Ml();l(Fe,yt)},$$slots:{default:!0}});var Be=v(Ne,2),Qe=v(h(Be)),Ct=v(Be,2);bl(Ct,{get onMCPServerAdd(){return le},get servers(){return n(T)}});var lt=v(Ct,2);ut(lt,1,()=>n(T),Fe=>Fe.id,(Fe,Ye)=>{const yt=xe(()=>(n(Oe),n(Ye),f(()=>n(Oe)===n(Ye).id?"edit":"view"))),Pt=xe(()=>(n(Ae),n(Ye),f(()=>n(Ae).errors.get(n(Ye).id)))),ur=xe(()=>(n(Ae),n(Ye),f(()=>n(Ae).warnings.get(n(Ye).id))));ta(Fe,{get mode(){return n(yt)},get server(){return n(Ye)},get onAdd(){return le},get onSave(){return Ve},get onDelete(){return ve},get onToggleDisableServer(){return ke},onEdit:ae,onCancel:q,get onJSONImport(){return ee},onAuthenticate:Ce,get disabledText(){return n(Pt)},get warningText(){return n(ur)}})});var qt=v(We,2),ws=Fe=>{ta(Fe,{get mode(){return n($)},get onAdd(){return le},get onSave(){return Ve},get onDelete(){return ve},get onToggleDisableServer(){return ke},onEdit:ae,onCancel:q,get onJSONImport(){return ee},onAuthenticate:Ce})};V(qt,Fe=>{n($)!=="add"&&n($)!=="addJson"&&n($)!=="addRemote"||Fe(ws)});var dt=v(qt,2),Ke=h(dt);Xe(Ke,{get disabled(){return n(ue)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{m($,"add")}},children:(Fe,Ye)=>{var yt=R("Add MCP");l(Fe,yt)},$$slots:{default:!0,iconLeft:(Fe,Ye)=>{ms(Fe,{slot:"iconLeft"})}}});var it=v(Ke,2);Xe(it,{get disabled(){return n(ue)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{m($,"addRemote")}},children:(Fe,Ye)=>{var yt=R("Add remote MCP");l(Fe,yt)},$$slots:{default:!0,iconLeft:(Fe,Ye)=>{ms(Fe,{slot:"iconLeft"})}}});var At=v(it,2),pt=Fe=>{Xe(Fe,{get disabled(){return n(ue)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{m($,"addJson")}},children:(Ye,yt)=>{var Pt=R("Import from JSON");l(Ye,Pt)},$$slots:{default:!0,iconLeft:(Ye,yt)=>{la(Ye,{slot:"iconLeft"})}}})};V(At,Fe=>{Ee()&&Fe(pt)}),ge(()=>es(Qe,"href",Pe)),l(te,Ue),rt(),Me()})(U,{get onMCPServerAdd(){return he()},get onMCPServerSave(){return we()},get onMCPServerDelete(){return D()},get onMCPServerToggleDisable(){return I()},get onMCPServerJSONImport(){return O()},get onCancel(){return re()},get isMCPImportEnabled(){return a()}})};V(G,U=>{s()&&U(L)});var y=v(G,2),_=U=>{Il(U,{get supportedShells(){return K()},get selectedShell(){return A()},get startupScript(){return C()},onShellSelect:j(),onStartupScriptChange:E()})};V(y,U=>{o()&&U(_)});var ie=v(y,2),z=U=>{(function(te,ce){st(ce,!1);const[Ie,Me]=Et(),Z=()=>nt(n(ue),"$currentSettings",Ie),ue=F(),Ae=F(),$e=ps($r.key);async function De(){return await $e.playAgentComplete(),"success"}me(()=>{},()=>{Ts(m(ue,$e.getCurrentSettings),"$currentSettings",Ie)}),me(()=>Z(),()=>{m(Ae,Z().enabled)}),ht(),at();var se=Pl(),Y=Se(se);de(Y,{size:1,weight:"regular",color:"secondary",children:(Ce,Oe)=>{var $=El();l(Ce,$)},$$slots:{default:!0}});var ze=v(Y,2),Te=h(ze);zt(Te,{$$slots:{"header-left":(Ce,Oe)=>{var $=Ll(),q=h($),T=h(q);de(T,{size:2,weight:"medium",children:(Q,W)=>{var ae=R("Enable Sound Effects");l(Q,ae)},$$slots:{default:!0}});var oe=v(q,2),X=h(oe);de(X,{size:1,weight:"medium",children:(Q,W)=>{var ae=R("Play a sound when an agent completes a task");l(Q,ae)},$$slots:{default:!0}}),l(Ce,$)},"header-right":(Ce,Oe)=>{var $=zl(),q=h($);br(q,{size:1,get checked(){return n(Ae)},$$events:{change:()=>$e.updateEnabled(!n(Ae))}}),l(Ce,$)}}});var Ee=v(Te,2),_e=Ce=>{zt(Ce,{$$slots:{"header-left":(Oe,$)=>{var q=Zl(),T=h(q),oe=h(T);de(oe,{size:2,weight:"medium",children:(W,ae)=>{var H=R("Test Sound");l(W,H)},$$slots:{default:!0}});var X=v(T,2),Q=h(X);de(Q,{size:1,weight:"medium",children:(W,ae)=>{var H=R("Play a sample of the agent completion sound");l(W,H)},$$slots:{default:!0}}),l(Oe,q)},"header-right":(Oe,$)=>{var q=Ol(),T=h(q);const oe=xe(()=>n(Ae)?"":"Enable sound effects to test"),X=xe(()=>(w(us),f(()=>[us.Hover])));Dt(T,{get content(){return n(oe)},get triggerOn(){return n(X)},children:(Q,W)=>{const ae=xe(()=>!n(Ae));wn(Q,{size:1,defaultColor:"neutral",get enabled(){return n(Ae)},stickyColor:!1,get disabled(){return n(ae)},onClick:De,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(H,le)=>{var Ve=R("Play");l(H,Ve)},$$slots:{default:!0}})},$$slots:{default:!0}}),l(Oe,q)}}})};V(Ee,Ce=>{n(Ae)&&Ce(_e)}),l(te,se),rt(),Me()})(U,{})};V(ie,U=>{i()&&U(z)});var k=v(ie,2),J=U=>{(function(te,ce){st(ce,!1);const[Ie,Me]=Et(),Z=()=>nt(n(ue),"$currentSettings",Ie),ue=F(),Ae=F();let $e=b(ce,"isSwarmModeEnabled",8,!1),De=b(ce,"hasEverUsedRemoteAgent",8,!1);const se=ps(qs.key);me(()=>{},()=>{Ts(m(ue,se.getCurrentSettings),"$currentSettings",Ie)}),me(()=>Z(),()=>{m(Ae,Z().enabled)}),ht(),at();var Y=ot(),ze=Se(Y),Te=Ee=>{var _e=Ul(),Ce=Se(_e);de(Ce,{size:1,weight:"regular",color:"secondary",children:($,q)=>{var T=Fl();l($,T)},$$slots:{default:!0}});var Oe=v(Ce,2);zt(h(Oe),{$$slots:{"header-left":($,q)=>{var T=jl(),oe=h(T),X=h(oe);de(X,{size:2,weight:"medium",children:(le,Ve)=>{var ee=R("Enable Swarm Mode");l(le,ee)},$$slots:{default:!0}});var Q=v(oe,2),W=h(Q);de(W,{size:1,weight:"medium",children:(le,Ve)=>{var ee=R("Allow agents to coordinate and work together on complex tasks");l(le,ee)},$$slots:{default:!0}});var ae=v(Q,2),H=h(ae);de(H,{size:1,weight:"regular",color:"secondary",children:(le,Ve)=>{var ee=R(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);l(le,ee)},$$slots:{default:!0}}),l($,T)},"header-right":($,q)=>{var T=Dl(),oe=h(T);br(oe,{size:1,get checked(){return n(Ae)},$$events:{change:()=>se.updateEnabled(!n(Ae))}}),l($,T)}}}),l(Ee,_e)};V(ze,Ee=>{$e()&&De()&&Ee(Te)}),l(te,Y),rt(),Me()})(U,{get isSwarmModeEnabled(){return c()},get hasEverUsedRemoteAgent(){return u()}})};V(k,U=>{d()&&U(J)}),l(r,x)}var Hl=$t("<svg><!></svg>");function Bl(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=Hl();_s(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),l(r,s)}var Gl=$t("<svg><!></svg>");function Jl(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=Gl();_s(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),l(r,s)}var Wl=$t("<svg><!></svg>");function sa(r,e){const t=rs(e,["children","$$slots","$$events","$$legacy"]);var s=Wl();_s(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...t}));var a=h(s);Hs(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',!0),l(r,s)}var Kl=g('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function Sa(r,e){st(e,!1);const[t,s]=Et(),a=Ir();let o=b(e,"userGuidelines",12,""),i=b(e,"userGuidelinesLengthLimit",24,()=>{}),d=b(e,"updateUserGuideline",8,()=>!1);const c=vt(void 0);function u(){const N=o().trim();if(nt(c,"$originalValue",t)!==N){if(!d()(N))throw i()&&N.length>i()?`The user guideline must be less than ${i()} character long`:"An error occurred updating the user";Fr(c,N)}}Za(()=>{Fr(c,o().trim())}),ra(()=>{u()}),at();var p=Kl(),pe=h(p);$n(pe,{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:u,get value(){return o()},set value(N){o(N)},$$events:{focus:N=>{a("focus",N)}},$$legacy:!0}),l(r,p),rt(),s()}var Yl=g("<!> <!> <!>",1),Xl=g('<div slot="footer"><!> <!></div>'),Ql=g('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),ed=g('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),td=g("<!> <!>",1),sd=g("<!> <!>",1),rd=g("<!> <!>",1),ad=g("<!> <!> <!> <!>",1),nd=g('<div slot="body" class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),id=g('<div slot="footer"><!> <!></div>');function od(r,e){st(e,!1);const[t,s]=Et(),a=()=>nt(n(he),"$focusedIndex",t),o=F(),i=Ir();let d=b(e,"show",8,!1),c=b(e,"options",24,()=>[]),u=b(e,"isLoading",8,!1),p=b(e,"errorMessage",8,""),pe=b(e,"successMessage",8,""),N=F(n(o)),he=F(void 0),we=F(()=>{});function D(){n(N)&&!u()&&i("select",n(N))}function I(){u()||(i("cancel"),m(N,n(o)))}me(()=>w(c()),()=>{m(o,c().length>0?c()[0]:null)}),me(()=>(w(d()),n(o)),()=>{d()&&m(N,n(o))}),ht(),at(),wt("keydown",Rr,function(O){d()&&!u()&&(O.key==="Escape"?(O.preventDefault(),I()):O.key==="Enter"&&n(N)&&(O.preventDefault(),D()))}),pa(r,{get show(){return d()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return u()},get preventEscapeClose(){return u()},$$events:{cancel:I},$$slots:{body:(O,re)=>{var K=nd(),A=h(K),C=E=>{var x=Ql();l(E,x)},j=E=>{var x=ad(),M=Se(x);de(M,{size:2,color:"secondary",children:(k,J)=>{var U=R("Select existing rules to auto import to .augment/rules");l(k,U)},$$slots:{default:!0}});var G=v(M,2);const L=xe(()=>(w(c()),f(()=>c().length===0?[]:void 0)));tt.Root(G,{get triggerOn(){return n(L)},get requestClose(){return n(we)},set requestClose(k){m(we,k)},get focusedIndex(){return n(he)},set focusedIndex(k){Ts(m(he,k),"$focusedIndex",t)},children:(k,J)=>{var U=rd(),te=Se(U);tt.Trigger(te,{children:(Ie,Me)=>{var Z=ed(),ue=h(Z),Ae=v(ue,2);Er(Ae,{class:"c-dropdown-chevron"}),ge(()=>Oa(ue,(n(N),f(()=>n(N)?n(N).label:"Existing rules")))),l(Ie,Z)},$$slots:{default:!0}});var ce=v(te,2);tt.Content(ce,{align:"start",side:"bottom",children:(Ie,Me)=>{var Z=sd(),ue=Se(Z);ut(ue,1,c,Gt,(De,se)=>{const Y=xe(()=>(n(N),n(se),f(()=>{var ze;return((ze=n(N))==null?void 0:ze.label)===n(se).label})));tt.Item(De,{onSelect:()=>function(ze){m(N,ze),n(we)()}(n(se)),get highlight(){return n(Y)},children:(ze,Te)=>{var Ee=R();ge(()=>Ze(Ee,(n(se),f(()=>n(se).label)))),l(ze,Ee)},$$slots:{default:!0}})});var Ae=v(ue,2),$e=De=>{var se=td(),Y=Se(se);tt.Separator(Y,{});var ze=v(Y,2);tt.Label(ze,{children:(Te,Ee)=>{var _e=R();ge(()=>Ze(_e,(a(),w(c()),n(N),f(()=>{var Ce;return a()!==void 0?c()[a()].description:(Ce=n(N))==null?void 0:Ce.description})))),l(Te,_e)},$$slots:{default:!0}}),l(De,se)};V(Ae,De=>{(a()!==void 0||n(N))&&De($e)}),l(Ie,Z)},$$slots:{default:!0}}),l(k,U)},$$slots:{default:!0},$$legacy:!0});var y=v(G,2),_=k=>{Ns(k,{variant:"soft",color:"error",size:1,children:(J,U)=>{var te=R();ge(()=>Ze(te,p())),l(J,te)},$$slots:{default:!0,icon:(J,U)=>{Qs(J,{slot:"icon"})}}})};V(y,k=>{p()&&k(_)});var ie=v(y,2),z=k=>{Ns(k,{variant:"soft",color:"success",size:1,children:(J,U)=>{var te=R();ge(()=>Ze(te,pe())),l(J,te)},$$slots:{default:!0,icon:(J,U)=>{tn(J,{slot:"icon"})}}})};V(ie,k=>{pe()&&k(z)}),l(E,x)};V(A,E=>{w(c()),f(()=>c().length===0)?E(C):E(j,!1)}),l(O,K)},footer:(O,re)=>{var K=id(),A=h(K);Xe(A,{variant:"solid",color:"neutral",get disabled(){return u()},$$events:{click:I},children:(E,x)=>{var M=R("Cancel");l(E,M)},$$slots:{default:!0}});var C=v(A,2),j=E=>{const x=xe(()=>!n(N)||u());Xe(E,{color:"accent",variant:"solid",get disabled(){return n(x)},get loading(){return u()},$$events:{click:D},children:(M,G)=>{var L=R();ge(()=>Ze(L,u()?"Importing...":"Import ")),l(M,L)},$$slots:{default:!0}})};V(C,E=>{w(c()),f(()=>c().length>0)&&E(j)}),l(O,K)}}}),rt(),s()}var ld=g('<div class="loading-container"><!> <!></div>'),dd=g('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),cd=g('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),ud=g('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),hd=g('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),pd=g('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),vd=g("<!> <!>",1),md=g("<!> <!>",1),gd=g("<!> <!>",1),fd=g(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function yd(r,e){st(e,!1);const[t,s]=Et(),a=()=>nt(re,"$rulesFiles",t),o=()=>nt(n(d),"$isRulesLoading",t),i=()=>nt(n(L),"$importFocusedIndex",t),d=F(),c=F(),u=F(),p=F();let pe=b(e,"userGuidelines",8,""),N=b(e,"userGuidelinesLengthLimit",24,()=>{}),he=b(e,"workspaceGuidelinesLengthLimit",24,()=>{}),we=b(e,"workspaceGuidelinesContent",8,""),D=b(e,"updateUserGuideline",8,()=>!1),I=b(e,"rulesModel",8),O=b(e,"rulesController",8);const re=I().getCachedRules(),K=O().getShowCreateRuleDialog(),A=O().getCreateRuleError();let C=F(!1),j=F([]),E=F(!1),x=F(""),M=F("");const G=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let L=F(void 0),y=F(()=>{});async function _($){try{$.id==="select_file_or_directory"?await O().selectFileToImport():$.id==="auto_import"&&await async function(){try{m(x,""),m(M,"");const q=await O().getAutoImportOptions();m(j,q.data.options),m(C,!0)}catch(q){console.error("Failed to get auto-import options:",q),m(x,"Failed to detect existing rules in workspace.")}}()}catch(q){console.error("Failed to handle import select:",q)}n(y)&&n(y)()}me(()=>w(I()),()=>{Ts(m(d,I().getLoading()),"$isRulesLoading",t)}),me(()=>w(he()),()=>{m(c,he())}),me(()=>(n(u),n(p),a(),w(we()),n(c)),()=>{var $;$=hn({rules:a(),workspaceGuidelinesContent:we(),rulesAndGuidelinesLimit:n(c)}),m(u,$.isOverLimit),m(p,$.warningMessage)}),ht(),at();var ie=fd(),z=Se(ie),k=h(z),J=h(k);de(J,{class:"c-section-header",size:3,color:"primary",children:($,q)=>{var T=R("Rules");l($,T)},$$slots:{default:!0}});var U=v(J,2),te=v(h(U)),ce=h(te);de(ce,{size:1,weight:"regular",children:($,q)=>{var T=R("Learn more");l($,T)},$$slots:{default:!0}});var Ie=v(U,2),Me=$=>{Ns($,{variant:"soft",color:"warning",size:1,children:(q,T)=>{var oe=R();ge(()=>Ze(oe,n(p))),l(q,oe)},$$slots:{default:!0,icon:(q,T)=>{Qs(q,{slot:"icon"})}}})};V(Ie,$=>{n(u)&&$(Me)});var Z=v(Ie,2),ue=h(Z),Ae=$=>{var q=ld(),T=h(q);Ys(T,{size:1});var oe=v(T,2);de(oe,{size:1,color:"secondary",children:(X,Q)=>{var W=R("Loading rules...");l(X,W)},$$slots:{default:!0}}),l($,q)},$e=($,q)=>{var T=X=>{var Q=dd(),W=h(Q);de(W,{size:1,color:"neutral",children:(ae,H)=>{var le=R("No rules files found");l(ae,le)},$$slots:{default:!0}}),l(X,Q)},oe=X=>{var Q=ot(),W=Se(Q);ut(W,1,a,ae=>ae.path,(ae,H)=>{zt(ae,{isClickable:!0,$$events:{click:()=>O().openRule(n(H).path)},$$slots:{"header-left":(le,Ve)=>{var ee=cd(),ve=h(ee),ke=h(ve),Pe=be=>{Dt(be,{content:"No description found",children:(Be,Qe)=>{Qs(Be,{})},$$slots:{default:!0}})},Ue=be=>{an(be,{})};V(ke,be=>{n(H),w(Dr),f(()=>n(H).type===Dr.AGENT_REQUESTED&&!n(H).description)?be(Pe):be(Ue,!1)});var We=v(ve,2),Ne=h(We);de(Ne,{size:1,color:"neutral",children:(be,Be)=>{var Qe=R();ge(()=>Ze(Qe,(n(H),f(()=>n(H).path)))),l(be,Qe)},$$slots:{default:!0}}),l(le,ee)},"header-right":(le,Ve)=>{var ee=ud(),ve=h(ee),ke=h(ve),Pe=h(ke);Sn(Pe,{get rule(){return n(H)},onSave:async(Ne,be)=>{await I().updateRuleContent({type:Ne,path:n(H).path,content:n(H).content,description:be})}});var Ue=v(ke,2);Xe(Ue,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ne=>{Ne.stopPropagation(),O().openRule(n(H).path)}},$$slots:{iconRight:(Ne,be)=>{sn(Ne,{slot:"iconRight"})}}});var We=v(Ue,2);Xe(We,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Ne=>{Ne.stopPropagation(),O().deleteRule(n(H).path)}},$$slots:{iconRight:(Ne,be)=>{rn(Ne,{slot:"iconRight"})}}}),l(le,ee)}}})}),l(X,Q)};V($,X=>{a(),f(()=>a().length===0)?X(T):X(oe,!1)},q)};V(ue,$=>{o(),a(),f(()=>o()&&a().length===0)?$(Ae):$($e,!1)});var De=v(Z,2),se=h(De);Xe(se,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>O().createRule()},children:($,q)=>{var T=hd(),oe=h(T);ms(oe,{slot:"iconLeft"}),l($,T)},$$slots:{default:!0}});var Y=v(se,2);tt.Root(Y,{get requestClose(){return n(y)},set requestClose($){m(y,$)},get focusedIndex(){return n(L)},set focusedIndex($){Ts(m(L,$),"$importFocusedIndex",t)},children:($,q)=>{var T=gd(),oe=Se(T);tt.Trigger(oe,{children:(Q,W)=>{Xe(Q,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(ae,H)=>{var le=pd(),Ve=h(le);la(Ve,{slot:"iconLeft"});var ee=v(Ve,2);Er(ee,{slot:"iconRight"}),l(ae,le)},$$slots:{default:!0}})},$$slots:{default:!0}});var X=v(oe,2);tt.Content(X,{align:"start",side:"bottom",children:(Q,W)=>{var ae=md(),H=Se(ae);ut(H,1,()=>G,ee=>ee.id,(ee,ve)=>{tt.Item(ee,{onSelect:()=>_(n(ve)),children:(ke,Pe)=>{var Ue=R();ge(()=>Ze(Ue,(n(ve),f(()=>n(ve).label)))),l(ke,Ue)},$$slots:{default:!0}})});var le=v(H,2),Ve=ee=>{var ve=vd(),ke=Se(ve);tt.Separator(ke,{});var Pe=v(ke,2);tt.Label(Pe,{children:(Ue,We)=>{var Ne=R();ge(()=>Ze(Ne,(i(),f(()=>i()!==void 0?G[i()].description:G[0])))),l(Ue,Ne)},$$slots:{default:!0}}),l(ee,ve)};V(le,ee=>{i()!==void 0&&ee(Ve)}),l(Q,ae)},$$slots:{default:!0}}),l($,T)},$$slots:{default:!0},$$legacy:!0});var ze=v(k,2),Te=h(ze);de(Te,{class:"c-section-header",size:3,color:"primary",children:($,q)=>{var T=R("User Guidelines");l($,T)},$$slots:{default:!0}});var Ee=v(Te,2),_e=v(h(Ee)),Ce=h(_e);de(Ce,{size:1,weight:"regular",children:($,q)=>{var T=R("Learn more");l($,T)},$$slots:{default:!0}}),Sa(v(Ee,2),{get userGuidelines(){return pe()},get userGuidelinesLengthLimit(){return N()},updateUserGuideline:D()});var Oe=v(z,2);(function($,q){st(q,!1);const T=Ir();let oe=b(q,"show",8,!1),X=b(q,"errorMessage",8,""),Q=F(""),W=F(void 0),ae=F(!1);function H(){n(Q).trim()&&!n(ae)&&(m(ae,!0),T("create",n(Q).trim()))}function le(){n(ae)||(T("cancel"),m(Q,""))}function Ve(ee){n(ae)||(ee.key==="Enter"?(ee.preventDefault(),H()):ee.key==="Escape"&&(ee.preventDefault(),le()))}me(()=>(w(oe()),n(W)),()=>{oe()&&n(W)&&setTimeout(()=>{var ee;return(ee=n(W))==null?void 0:ee.focus()},100)}),me(()=>(w(oe()),w(X())),()=>{oe()&&!X()||m(ae,!1)}),me(()=>(w(oe()),w(X())),()=>{oe()||X()||m(Q,"")}),ht(),at(),pa($,{get show(){return oe()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return n(ae)},get preventEscapeClose(){return n(ae)},$$events:{cancel:le,keydown:function(ee){n(ae)||ee.detail.key==="Enter"&&(ee.detail.preventDefault(),H())}},$$slots:{body:(ee,ve)=>{var ke=Yl(),Pe=Se(ke);de(Pe,{size:2,color:"secondary",children:(be,Be)=>{var Qe=R("Enter a name for the new rule file (e.g., architecture.md):");l(be,Qe)},$$slots:{default:!0}});var Ue=v(Pe,2);ts(Ue,{placeholder:"rule-name.md",get disabled(){return n(ae)},get value(){return n(Q)},set value(be){m(Q,be)},get textInput(){return n(W)},set textInput(be){m(W,be)},$$events:{keydown:Ve},$$legacy:!0});var We=v(Ue,2),Ne=be=>{Ns(be,{variant:"soft",color:"error",size:1,children:(Be,Qe)=>{var Ct=R();ge(()=>Ze(Ct,X())),l(Be,Ct)},$$slots:{default:!0,icon:(Be,Qe)=>{Qs(Be,{slot:"icon"})}}})};V(We,be=>{X()&&be(Ne)}),l(ee,ke)},footer:(ee,ve)=>{var ke=Xl(),Pe=h(ke);Xe(Pe,{variant:"solid",color:"neutral",get disabled(){return n(ae)},$$events:{click:le},children:(Ne,be)=>{var Be=R("Cancel");l(Ne,Be)},$$slots:{default:!0}});var Ue=v(Pe,2);const We=xe(()=>(n(Q),n(ae),f(()=>!n(Q).trim()||n(ae))));Xe(Ue,{variant:"solid",color:"accent",get disabled(){return n(We)},get loading(){return n(ae)},$$events:{click:H},children:(Ne,be)=>{var Be=R();ge(()=>Ze(Be,n(ae)?"Creating...":"Create")),l(Ne,Be)},$$slots:{default:!0}}),l(ee,ke)}}}),rt()})(Oe,{get show(){return nt(K,"$showCreateRuleDialog",t)},get errorMessage(){return nt(A,"$createRuleError",t)},$$events:{create:function($){O().handleCreateRuleWithName($.detail)},cancel:function(){O().hideCreateRuleDialog()}}}),od(v(Oe,2),{get show(){return n(C)},get options(){return n(j)},get isLoading(){return n(E)},get errorMessage(){return n(x)},get successMessage(){return n(M)},$$events:{select:async function($){const q=$.detail;try{m(E,!0),m(x,"");const T=await O().processAutoImportSelection(q);let oe=`Successfully imported ${T.importedRulesCount} rule${T.importedRulesCount!==1?"s":""} from ${q.label}`;T.duplicatesCount>0&&(oe+=`, ${T.duplicatesCount} duplicate${T.duplicatesCount!==1?"s":""} skipped`),T.totalAttempted>T.importedRulesCount+T.duplicatesCount&&(oe+=`, ${T.totalAttempted-T.importedRulesCount-T.duplicatesCount} failed`),m(M,oe),setTimeout(()=>{m(C,!1),m(M,"")},500)}catch(T){console.error("Failed to process auto-import selection:",T),m(x,"Failed to import rules. Please try again.")}finally{m(E,!1)}},cancel:function(){m(C,!1),m(x,""),m(M,"")}}}),l(r,ie),rt(),s()}var _d=$t("<svg><!></svg>");function wd(r,e){st(e,!1);let t=b(e,"onSignOut",8),s=F(!1);at(),Xe(r,{get loading(){return n(s)},variant:"soft","data-testid":"sign-out-button",$$events:{click:function(){t()(),m(s,!0)}},children:(a,o)=>{var i=R("Sign Out");l(a,i)},$$slots:{default:!0,iconLeft:(a,o)=>{(function(i,d){const c=rs(d,["children","$$slots","$$events","$$legacy"]);var u=_d();_s(u,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...c}));var p=h(u);Hs(p,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),l(i,u)})(a,{slot:"iconLeft"})}}}),rt()}class Cd{constructor(e,t,s){je(this,"_showCreateRuleDialog",vt(!1));je(this,"_createRuleError",vt(""));je(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=s;const a=new ia;this._extensionClient=new oa(e,t,a)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:fr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Js.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const s=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(s)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===Vr?this._extensionClient.openFile({repoRoot:t,pathName:Vr}):this._extensionClient.openFile({repoRoot:t,pathName:`${on}/${ln}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:Je.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?Js.selectedDirectory:(e.directoryOrFile,Js.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:fr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:fr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Js.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var bd=g('<span slot="content"><!></span>');function $d(r,e){st(e,!1);const[t,s]=Et(),a=()=>nt(z,"$guidelines",t),o=()=>nt(E,"$settingsComponentSupported",t),i=()=>nt(x,"$enableAgentMode",t),d=()=>nt(L,"$terminalSettingsStore",t),c=F(),u=F(),p=F(),pe=F(),N=F(),he=new Rs(ct),we=new Gs(ct),D=new Fi(ct),I=new Ua(ct),O=new ia,re=new oa(ct,I,O),K=new $r(I),A=new qs(I),C=new dn(I),j=new Cd(ct,I,C);I.registerConsumer(C),$s($r.key,K),$s(qs.key,A),$s(Rs.key,he),function(Z){$s(Ca,Z)}(re),function(Z){$s($a,Z)}(we);const E=he.getSettingsComponentSupported(),x=he.getEnableAgentMode(),M=he.getEnableAgentSwarmMode(),G=he.getHasEverUsedRemoteAgent();I.registerConsumer(he),I.registerConsumer(we),I.registerConsumer(D);const L=D.getTerminalSettings();let y=F();const _={handleMessageFromExtension:Z=>!(!Z.data||Z.data.type!==Je.navigateToSettingsSection)&&(Z.data.data&&typeof Z.data.data=="string"&&m(y,Z.data.data),!0)};I.registerConsumer(_);const ie=he.getDisplayableTools(),z=he.getGuidelines();function k(Z){const ue=Z.trim();return!(n(u)&&ue.length>n(u))&&(he.updateLocalUserGuidelines(ue),ct.postMessage({type:Je.updateUserGuidelines,data:Z}),!0)}function J(Z){ct.postMessage({type:Je.toolConfigStartOAuth,data:{authUrl:Z}}),he.startPolling()}async function U(Z){await re.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${Z.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&ct.postMessage({type:Je.toolConfigRevokeAccess,data:{toolId:Z.identifier}})}function te(Z){D.updateSelectedShell(Z)}function ce(Z){D.updateStartupScript(Z)}function Ie(Z,ue){ct.postMessage({type:Je.toolApprovalConfigSetRequest,data:{toolId:Z,approvalConfig:ue}})}function Me(){ct.postMessage({type:Je.signOut})}ra(()=>{he.dispose(),K.dispose(),A.dispose()}),he.notifyLoaded(),ct.postMessage({type:Je.getOrientationStatus}),ct.postMessage({type:Je.settingsPanelLoaded}),me(()=>a(),()=>{var Z;m(c,(Z=a().userGuidelines)==null?void 0:Z.contents)}),me(()=>a(),()=>{var Z;m(u,(Z=a().userGuidelines)==null?void 0:Z.lengthLimit)}),me(()=>a(),()=>{var Z,ue;m(p,(ue=(Z=a().workspaceGuidelines)==null?void 0:Z[0])==null?void 0:ue.lengthLimit)}),me(()=>a(),()=>{var Z,ue;m(pe,((ue=(Z=a().workspaceGuidelines)==null?void 0:Z[0])==null?void 0:ue.contents)||"")}),me(()=>(o(),sa),()=>{m(N,[o().remoteTools?ks("Tools","",Co,"section-tools"):void 0,o().userGuidelines&&!o().rules?ks("User Guidelines","Guidelines for Augment Chat to follow.",Bl,"guidelines"):void 0,o().rules?ks("Rules and User Guidelines","",Jl,"guidelines"):void 0,o().workspaceContext?ks("Context","",$o,"context"):void 0,ks("Account","Manage your Augment account settings.",sa,"account")].filter(Boolean))}),me(()=>(n(N),n(y)),()=>{var Z;n(N).length>1&&!n(y)&&m(y,(Z=n(N)[0])==null?void 0:Z.id)}),ht(),at(),wt("message",Rr,function(...Z){var ue;(ue=I.onMessageFromExtension)==null||ue.apply(this,Z)}),bn.Root(r,{children:(Z,ue)=>{_o(Z,{get items(){return n(N)},mode:"tree",class:"c-settings-navigation",get selectedId(){return n(y)},$$slots:{content:(Ae,$e)=>{var De=bd();const se=xe(()=>$e.item);var Y=h(De),ze=Ee=>{},Te=(Ee,_e)=>{var Ce=$=>{so($,{})},Oe=($,q)=>{var T=X=>{var Q=ot(),W=Se(Q),ae=le=>{yd(le,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},get workspaceGuidelinesLengthLimit(){return n(p)},get workspaceGuidelinesContent(){return n(pe)},updateUserGuideline:k,get rulesModel(){return C},get rulesController(){return j}})},H=le=>{Sa(le,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},updateUserGuideline:k})};V(W,le=>{o(),f(()=>o().rules)?le(ae):le(H,!1)}),l(X,Q)},oe=(X,Q)=>{var W=H=>{wd(H,{onSignOut:Me})},ae=H=>{const le=xe(()=>(i(),o(),f(()=>i()&&o().mcpServerList))),Ve=xe(()=>(i(),o(),f(()=>i()&&o().mcpServerImport)));ql(H,{get tools(){return nt(ie,"$displayableTools",t)},onAuthenticate:J,onRevokeAccess:U,onToolApprovalConfigChange:Ie,onMCPServerAdd:ee=>we.addServer(ee),onMCPServerSave:ee=>we.updateServer(ee),onMCPServerDelete:ee=>we.deleteServer(ee),onMCPServerToggleDisable:ee=>we.toggleDisabledServer(ee),onMCPServerJSONImport:ee=>we.importServersFromJSON(ee),get isMCPEnabled(){return n(le)},get isMCPImportEnabled(){return n(Ve)},get supportedShells(){return d(),f(()=>d().supportedShells)},get selectedShell(){return d(),f(()=>d().selectedShell)},get startupScript(){return d(),f(()=>d().startupScript)},onShellSelect:te,onStartupScriptChange:ce,get isTerminalEnabled(){return o(),f(()=>o().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return i()},get isSwarmModeFeatureFlagEnabled(){return nt(M,"$enableAgentSwarmMode",t)},get hasEverUsedRemoteAgent(){return nt(G,"$hasEverUsedRemoteAgent",t)}})};V(X,H=>{w(n(se)),f(()=>{var le;return((le=n(se))==null?void 0:le.id)==="account"})?H(W):H(ae,!1)},Q)};V($,X=>{w(n(se)),f(()=>{var Q;return((Q=n(se))==null?void 0:Q.id)==="guidelines"})?X(T):X(oe,!1)},q)};V(Ee,$=>{w(n(se)),f(()=>{var q;return((q=n(se))==null?void 0:q.id)==="context"})?$(Ce):$(Oe,!1)},_e)};V(Y,Ee=>{w(Tr),w(n(se)),f(()=>!Tr(n(se)))?Ee(ze):Ee(Te,!1)}),l(Ae,De)}}})},$$slots:{default:!0}}),rt(),s()}(async function(){ct&&ct.initialize&&await ct.initialize(),Pa($d,{target:document.getElementById("app")})})();
